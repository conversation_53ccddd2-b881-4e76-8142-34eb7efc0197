<?php
/**
 * Fix script to resolve common issues
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Clear session
session_unset();
session_destroy();

// Start a new session
session_start();

// Clear cookies
if (isset($_SERVER['HTTP_COOKIE'])) {
    $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
    foreach($cookies as $cookie) {
        $parts = explode('=', $cookie);
        $name = trim($parts[0]);
        setcookie($name, '', time()-1000);
        setcookie($name, '', time()-1000, '/');
    }
}

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check database connection
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    $dbStatus = "Connected successfully to database";
    
    // Check if superadmin user exists
    $stmt = $conn->query('SELECT * FROM users WHERE username = "superadmin"');
    $user = $stmt->fetch();
    
    if ($user) {
        $userStatus = "Superadmin user found";
        
        // Reset superadmin password
        $password = 'password';
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare('UPDATE users SET password = :password WHERE username = "superadmin"');
        $stmt->bindParam(':password', $hashedPassword);
        
        if ($stmt->execute()) {
            $passwordStatus = "Superadmin password reset to 'password'";
        } else {
            $passwordStatus = "Failed to reset superadmin password";
        }
    } else {
        $userStatus = "Superadmin user not found";
        $passwordStatus = "Cannot reset password";
    }
} catch (PDOException $e) {
    $dbStatus = "Database connection failed: " . $e->getMessage();
    $userStatus = "Cannot check user";
    $passwordStatus = "Cannot reset password";
}

// Create a .htaccess backup
if (file_exists('.htaccess')) {
    copy('.htaccess', '.htaccess.backup');
    $htaccessBackup = "Created .htaccess backup";
} else {
    $htaccessBackup = "No .htaccess file found";
}

// Update .htaccess file
$htaccess = <<<EOT
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /ProjectUAS/
    
    # Prevent redirect loops
    RewriteCond %{ENV:REDIRECT_STATUS} 200
    RewriteRule .* - [L]
    
    # Redirect to index.php if not a file or directory
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
</IfModule>

# PHP settings
php_flag display_errors on
php_value error_reporting E_ALL

# Default index
DirectoryIndex index.php

# Prevent infinite loops
<IfModule mod_headers.c>
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</IfModule>
EOT;

file_put_contents('.htaccess', $htaccess);
$htaccessUpdate = "Updated .htaccess file";

// Output results
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Script - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">Fix Script Results</h4>
                    </div>
                    <div class="card-body">
                        <h5>Session and Cookies</h5>
                        <p>Session cleared and cookies removed.</p>
                        
                        <h5>Database</h5>
                        <p><?php echo $dbStatus; ?></p>
                        <p><?php echo $userStatus; ?></p>
                        <p><?php echo $passwordStatus; ?></p>
                        
                        <h5>Files</h5>
                        <p><?php echo $htaccessBackup; ?></p>
                        <p><?php echo $htaccessUpdate; ?></p>
                        
                        <h5>Next Steps</h5>
                        <p>The system has been reset. You can now try to login again.</p>
                        
                        <div class="mt-4">
                            <a href="direct_login.php" class="btn btn-primary">Go to Direct Login</a>
                            <a href="index.php" class="btn btn-secondary">Go to Homepage</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

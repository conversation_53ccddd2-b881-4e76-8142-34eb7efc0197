<?php
/**
 * Direct login page that bypasses the router
 * Use this if you're experiencing redirect loops
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Clear any existing session data
session_unset();

// Define data for the login form
$data = [
    'username' => '',
    'password' => '',
    'username_err' => '',
    'password_err' => ''
];

// Process login form if submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Load database connection
    require_once BASE_PATH . '/core/Database.php';
    require_once BASE_PATH . '/core/Model.php';
    require_once BASE_PATH . '/models/User.php';
    require_once BASE_PATH . '/models/Client.php';
    
    try {
        // Create user model
        $userModel = new User();
        $clientModel = new Client();
        
        // Get form data
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        
        // Validate username
        if (empty($username)) {
            $data['username_err'] = 'Please enter username';
        }
        
        // Validate password
        if (empty($password)) {
            $data['password_err'] = 'Please enter password';
        }
        
        // Check for user
        $user = $userModel->getUserByUsername($username);
        
        if (!$user) {
            // User not found
            $data['username_err'] = 'No user found';
        }
        
        // Make sure errors are empty
        if (empty($data['username_err']) && empty($data['password_err'])) {
            // Validated
            // Check and set logged in user
            if (password_verify($password, $user->password)) {
                // Create session
                $_SESSION['user_id'] = $user->id;
                $_SESSION['user_username'] = $user->username;
                $_SESSION['user_email'] = $user->email;
                $_SESSION['user_name'] = $user->full_name;
                $_SESSION['user_role'] = $user->role;
                
                // If admin, set client ID
                if ($user->role == 'admin' && $user->client_id) {
                    $_SESSION['client_id'] = $user->client_id;
                    
                    // Get client info
                    $client = $clientModel->getClientById($user->client_id);
                    if ($client) {
                        $_SESSION['client_name'] = $client->name;
                        $_SESSION['rental_type'] = $client->rental_type;
                    }
                }
                
                // Reset redirect count
                $_SESSION['redirect_count'] = 0;
                
                // Redirect to dashboard
                header('Location: ' . APP_URL . '/dashboard');
                exit;
            } else {
                $data['password_err'] = 'Password incorrect';
            }
        }
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
        exit;
    }
}

// Include the login view
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Login to <?php echo APP_NAME; ?></h4>
                    </div>
                    <div class="card-body">
                        <form action="direct_login.php" method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control <?php echo (!empty($data['username_err'])) ? 'is-invalid' : ''; ?>" id="username" name="username" value="<?php echo $data['username']; ?>">
                                <div class="invalid-feedback"><?php echo $data['username_err']; ?></div>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control <?php echo (!empty($data['password_err'])) ? 'is-invalid' : ''; ?>" id="password" name="password">
                                <div class="invalid-feedback"><?php echo $data['password_err']; ?></div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Login</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="mt-3 text-center">
                    <p>Default credentials:</p>
                    <ul class="list-unstyled">
                        <li>Username: superadmin</li>
                        <li>Password: password</li>
                    </ul>
                </div>
                
                <div class="mt-3 text-center">
                    <a href="reset_password.php" class="btn btn-sm btn-warning">Reset Password</a>
                    <a href="clear_session.php" class="btn btn-sm btn-danger">Clear Session</a>
                    <a href="debug.php" class="btn btn-sm btn-info">Debug Info</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * Create Missing Tables Script
 * This script creates the missing 'rentals' table
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load configuration
require_once __DIR__ . '/config/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Create Missing Tables</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h2>Creating Missing Tables</h2>";

try {
    // Connect to database
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    echo "<p class='success'>✅ Database connection successful!</p>";
    
    // Check if rentals table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'rentals'");
    $rentals_exists = $stmt->rowCount() > 0;
    
    if (!$rentals_exists) {
        echo "<p class='info'>📋 Creating rentals table...</p>";
        
        // Create rentals table
        $sql = "CREATE TABLE IF NOT EXISTS rentals (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            item_id INT NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            customer_phone VARCHAR(20),
            customer_email VARCHAR(100),
            customer_address TEXT,
            rental_start_date DATE NOT NULL,
            rental_end_date DATE NOT NULL,
            actual_return_date DATE NULL,
            total_days INT NOT NULL,
            price_per_day DECIMAL(10,2) NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            deposit_amount DECIMAL(10,2) DEFAULT 0,
            status ENUM('active', 'returned', 'overdue', 'cancelled') DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
            FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
        )";
        
        $conn->exec($sql);
        echo "<p class='success'>✅ Rentals table created successfully!</p>";
    } else {
        echo "<p class='info'>ℹ️ Rentals table already exists.</p>";
    }
    
    // Verify all required tables now exist
    $required_tables = ['users', 'clients', 'services', 'menus', 'submenus', 'client_services', 'client_menus', 'client_submenus', 'items', 'rentals'];
    $existing_tables = [];
    
    $stmt = $conn->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }
    
    $missing_tables = array_diff($required_tables, $existing_tables);
    
    if (empty($missing_tables)) {
        echo "<p class='success'>✅ All required tables now exist!</p>";
        echo "<h3 class='success'>🎉 Database Setup Complete!</h3>";
        echo "<p>Your database is now ready for use.</p>";
    } else {
        echo "<p class='error'>❌ Still missing tables: " . implode(', ', $missing_tables) . "</p>";
    }
    
    // Show current table list
    echo "<h4>Current Tables in Database:</h4>";
    echo "<ul>";
    foreach ($existing_tables as $table) {
        $is_required = in_array($table, $required_tables);
        $class = $is_required ? 'success' : 'info';
        $icon = $is_required ? '✅' : 'ℹ️';
        echo "<li class='$class'>$icon $table</li>";
    }
    echo "</ul>";
    
    echo "<hr>";
    echo "<h3>Next Steps:</h3>";
    echo "<p><a href='test_comprehensive.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🔄 Re-run Comprehensive Test</a></p>";
    echo "<p><a href='login.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>🚀 Go to Login Page</a></p>";
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration.</p>";
}

echo "</body></html>";
?>

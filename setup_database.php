<?php
/**
 * Database setup script
 * 
 * This script will create the database and tables if they don't exist
 * and insert default data.
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'saas_rental');

// Connect to MySQL server (without database)
try {
    $conn = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $conn->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME);
    echo "Database created or already exists<br>";
    
    // Connect to the database
    $conn = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create users table
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('superadmin', 'admin') NOT NULL,
        client_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    echo "Users table created or already exists<br>";
    
    // Create clients table
    $sql = "CREATE TABLE IF NOT EXISTS clients (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        rental_type ENUM('bicycle', 'motorcycle') NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    echo "Clients table created or already exists<br>";
    
    // Create services table
    $sql = "CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    echo "Services table created or already exists<br>";
    
    // Create client_services table
    $sql = "CREATE TABLE IF NOT EXISTS client_services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        service_id INT NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
        FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
        UNIQUE KEY (client_id, service_id)
    )";
    $conn->exec($sql);
    echo "Client_services table created or already exists<br>";
    
    // Create menus table
    $sql = "CREATE TABLE IF NOT EXISTS menus (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        url VARCHAR(255),
        order_number INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    echo "Menus table created or already exists<br>";
    
    // Create submenus table
    $sql = "CREATE TABLE IF NOT EXISTS submenus (
        id INT AUTO_INCREMENT PRIMARY KEY,
        menu_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        url VARCHAR(255) NOT NULL,
        order_number INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (menu_id) REFERENCES menus(id) ON DELETE CASCADE
    )";
    $conn->exec($sql);
    echo "Submenus table created or already exists<br>";
    
    // Create client_menus table
    $sql = "CREATE TABLE IF NOT EXISTS client_menus (
        id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        menu_id INT NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
        FOREIGN KEY (menu_id) REFERENCES menus(id) ON DELETE CASCADE,
        UNIQUE KEY (client_id, menu_id)
    )";
    $conn->exec($sql);
    echo "Client_menus table created or already exists<br>";
    
    // Create client_submenus table
    $sql = "CREATE TABLE IF NOT EXISTS client_submenus (
        id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        submenu_id INT NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
        FOREIGN KEY (submenu_id) REFERENCES submenus(id) ON DELETE CASCADE,
        UNIQUE KEY (client_id, submenu_id)
    )";
    $conn->exec($sql);
    echo "Client_submenus table created or already exists<br>";

    // Create items table
    $sql = "CREATE TABLE IF NOT EXISTS items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        type ENUM('bicycle', 'motorcycle') NOT NULL,
        brand VARCHAR(50),
        model VARCHAR(50),
        year INT,
        color VARCHAR(30),
        price_per_day DECIMAL(10,2) NOT NULL,
        status ENUM('available', 'rented', 'maintenance', 'inactive') DEFAULT 'available',
        image_url VARCHAR(255),
        serial_number VARCHAR(50),
        condition_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
        INDEX idx_client_type (client_id, type),
        INDEX idx_status (status)
    )";
    $conn->exec($sql);
    echo "Items table created or already exists<br>";

    // Add some sample items if table is empty
    $stmt = $conn->prepare("SELECT COUNT(*) FROM items");
    $stmt->execute();
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        // Get client IDs
        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'bicycle'");
        $stmt->execute();
        $clientA = $stmt->fetchColumn();

        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'motorcycle'");
        $stmt->execute();
        $clientB = $stmt->fetchColumn();

        // Insert sample items
        $sql = "INSERT INTO items (client_id, name, description, type, brand, model, year, color, price_per_day, status, serial_number) VALUES
                ($clientA, 'Mountain Bike Pro', 'Professional mountain bike for rough terrain', 'bicycle', 'Trek', 'X-Caliber 8', 2023, 'Red', 50000, 'available', 'MTB001'),
                ($clientA, 'City Bike Comfort', 'Comfortable city bike for daily commuting', 'bicycle', 'Giant', 'Escape 3', 2022, 'Blue', 35000, 'available', 'CTB001'),
                ($clientB, 'Sport Motorcycle', 'High performance sport motorcycle', 'motorcycle', 'Honda', 'CBR150R', 2023, 'Black', 150000, 'available', 'SPT001'),
                ($clientB, 'Scooter Matic', 'Automatic scooter for city riding', 'motorcycle', 'Yamaha', 'NMAX 155', 2022, 'White', 100000, 'available', 'SCT001')";
        $conn->exec($sql);
        echo "Sample items created<br>";
    } else {
        echo "Items already exist<br>";
    }

    // Check if superadmin user exists
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = 'superadmin'");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert superadmin user
        $password = password_hash('password', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, email, full_name, role)
                VALUES ('superadmin', :password, '<EMAIL>', 'Super Admin', 'superadmin')";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':password', $password);
        $stmt->execute();
        echo "Superadmin user created<br>";
    } else {
        echo "Superadmin user already exists<br>";
    }

    // Check if admin users exist
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
    $stmt->execute();
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        // Get client IDs
        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'bicycle'");
        $stmt->execute();
        $clientA = $stmt->fetchColumn();

        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'motorcycle'");
        $stmt->execute();
        $clientB = $stmt->fetchColumn();

        // Insert admin users
        $password = password_hash('admin123', PASSWORD_DEFAULT);

        $sql = "INSERT INTO users (username, password, email, full_name, role, client_id)
                VALUES
                ('admin_bicycle', :password1, '<EMAIL>', 'Admin Bicycle', 'admin', :client_a),
                ('admin_motorcycle', :password2, '<EMAIL>', 'Admin Motorcycle', 'admin', :client_b)";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':password1', $password);
        $stmt->bindParam(':password2', $password);
        $stmt->bindParam(':client_a', $clientA);
        $stmt->bindParam(':client_b', $clientB);
        $stmt->execute();
        echo "Default admin users created (password: admin123)<br>";
    } else {
        echo "Admin users already exist<br>";
    }
    
    // Check if default clients exist
    $stmt = $conn->prepare("SELECT COUNT(*) FROM clients");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert default clients
        $sql = "INSERT INTO clients (name, description, rental_type)
                VALUES 
                ('Client A', 'Bicycle Rental Service', 'bicycle'),
                ('Client B', 'Motorcycle Rental Service', 'motorcycle')";
        $conn->exec($sql);
        echo "Default clients created<br>";
    } else {
        echo "Clients already exist<br>";
    }
    
    // Check if default services exist
    $stmt = $conn->prepare("SELECT COUNT(*) FROM services");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert default services
        $sql = "INSERT INTO services (name, description)
                VALUES 
                ('Rental Management', 'Manage rental items, bookings, and returns'),
                ('Customer Management', 'Manage customer information and history'),
                ('Reporting', 'Generate and view reports'),
                ('Maintenance Tracking', 'Track maintenance schedules and history')";
        $conn->exec($sql);
        echo "Default services created<br>";
    } else {
        echo "Services already exist<br>";
    }
    
    // Check if default menus exist
    $stmt = $conn->prepare("SELECT COUNT(*) FROM menus");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert default menus
        $sql = "INSERT INTO menus (name, icon, url, order_number)
                VALUES 
                ('Dashboard', 'fas fa-tachometer-alt', 'dashboard', 1),
                ('User Management', 'fas fa-users', 'users', 2),
                ('Clients', 'fas fa-building', 'clients', 3),
                ('Services', 'fas fa-cogs', 'services', 4),
                ('Menus', 'fas fa-list', 'menus', 5),
                ('Bicycles', 'fas fa-bicycle', 'bicycles', 6),
                ('Motorcycles', 'fas fa-motorcycle', 'motorcycles', 7),
                ('Rentals', 'fas fa-calendar-check', 'rentals', 8),
                ('Customers', 'fas fa-user-friends', 'customers', 9),
                ('Reports', 'fas fa-chart-bar', 'reports', 10),
                ('Settings', 'fas fa-cog', 'settings', 11)";
        $conn->exec($sql);
        echo "Default menus created<br>";
        
        // Get client IDs
        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'bicycle'");
        $stmt->execute();
        $clientA = $stmt->fetchColumn();
        
        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'motorcycle'");
        $stmt->execute();
        $clientB = $stmt->fetchColumn();
        
        // Assign menus to clients
        $sql = "INSERT INTO client_menus (client_id, menu_id)
                VALUES 
                ($clientA, 1), -- Dashboard for Client A
                ($clientB, 1), -- Dashboard for Client B
                ($clientA, 9), -- Customers for Client A
                ($clientB, 9), -- Customers for Client B
                ($clientA, 8), -- Rentals for Client A
                ($clientB, 8), -- Rentals for Client B
                ($clientA, 10), -- Reports for Client A
                ($clientB, 10), -- Reports for Client B
                ($clientA, 11), -- Settings for Client A
                ($clientB, 11), -- Settings for Client B
                ($clientA, 6), -- Bicycles only for Client A
                ($clientB, 7)  -- Motorcycles only for Client B";
        $conn->exec($sql);
        echo "Menus assigned to clients<br>";
    } else {
        echo "Menus already exist<br>";
    }
    
    // Check if client services exist
    $stmt = $conn->prepare("SELECT COUNT(*) FROM client_services");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Get client IDs
        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'bicycle'");
        $stmt->execute();
        $clientA = $stmt->fetchColumn();
        
        $stmt = $conn->prepare("SELECT id FROM clients WHERE rental_type = 'motorcycle'");
        $stmt->execute();
        $clientB = $stmt->fetchColumn();
        
        // Assign services to clients
        $sql = "INSERT INTO client_services (client_id, service_id)
                VALUES 
                ($clientA, 1), -- Rental Management for Client A
                ($clientA, 2), -- Customer Management for Client A
                ($clientA, 3), -- Reporting for Client A
                ($clientB, 1), -- Rental Management for Client B
                ($clientB, 2), -- Customer Management for Client B
                ($clientB, 3), -- Reporting for Client B
                ($clientB, 4)  -- Maintenance Tracking only for Client B";
        $conn->exec($sql);
        echo "Services assigned to clients<br>";
    } else {
        echo "Client services already exist<br>";
    }
    
    echo "<br><strong>Database setup completed successfully!</strong><br>";
    echo "<a href='login.php' class='btn btn-primary'>Go to Login Page</a>";
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}

// Close connection
$conn = null;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0d6efd;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Setup</h1>
        <hr>
        <!-- Results will be displayed above this line -->
        <a href="login.php" class="btn btn-primary">Go to Login Page</a>
    </div>
</body>
</html>

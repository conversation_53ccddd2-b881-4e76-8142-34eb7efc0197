// Main JavaScript file for SAAS Service Rental

// Document ready function
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').alert('close');
    }, 5000);
    
    // Confirm delete
    $('.btn-delete').on('click', function(e) {
        if (!confirm('Are you sure you want to delete this item?')) {
            e.preventDefault();
        }
    });
    
    // Role change handler for user form
    $('#role').on('change', function() {
        if ($(this).val() === 'admin') {
            $('#client-container').removeClass('d-none');
            $('#client_id').prop('required', true);
        } else {
            $('#client-container').addClass('d-none');
            $('#client_id').prop('required', false);
        }
    });
    
    // Initialize role change handler on page load
    if ($('#role').length) {
        $('#role').trigger('change');
    }
});

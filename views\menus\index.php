<?php require_once BASE_PATH . '/views/includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Menu Management</h4>
                <a href="<?php echo APP_URL; ?>/menus/add" class="btn btn-light">Add Menu</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Icon</th>
                                <th>URL</th>
                                <th>Order</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($data['menus'] as $menu) : ?>
                                <tr>
                                    <td><?php echo $menu->id; ?></td>
                                    <td><?php echo $menu->name; ?></td>
                                    <td><i class="<?php echo $menu->icon; ?>"></i> <?php echo $menu->icon; ?></td>
                                    <td><?php echo $menu->url; ?></td>
                                    <td><?php echo $menu->order_number; ?></td>
                                    <td>
                                        <?php if($menu->status == 'active') : ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else : ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo APP_URL; ?>/menus/edit/<?php echo $menu->id; ?>" class="btn btn-sm btn-primary">Edit</a>
                                        <a href="<?php echo APP_URL; ?>/menus/submenus/<?php echo $menu->id; ?>" class="btn btn-sm btn-info">Submenus</a>
                                        <form class="d-inline" action="<?php echo APP_URL; ?>/menus/delete/<?php echo $menu->id; ?>" method="post">
                                            <button type="submit" class="btn btn-sm btn-danger btn-delete">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once BASE_PATH . '/views/includes/footer.php'; ?>

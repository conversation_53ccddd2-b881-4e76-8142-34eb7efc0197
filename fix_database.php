<?php
/**
 * Fix Database Script - Creates missing tables and columns
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load configuration
require_once __DIR__ . '/config/config.php';

echo "<h2>Database Fix Script</h2>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px;'>";

try {
    // Connect to database
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Check if items table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'items'");
    $table_exists = $stmt->rowCount() > 0;
    
    if (!$table_exists) {
        echo "<p style='color: orange;'>⚠️ Items table not found. Creating...</p>";
        
        // Create items table
        $sql = "CREATE TABLE IF NOT EXISTS items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            brand VARCHAR(50),
            model VARCHAR(50),
            serial_number VARCHAR(100) UNIQUE,
            year INT,
            color VARCHAR(30),
            price_per_day DECIMAL(10,2) NOT NULL,
            status ENUM('available', 'rented', 'maintenance', 'inactive') DEFAULT 'available',
            image_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ Items table created successfully!</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Items table already exists.</p>";
        
        // Check if all required columns exist
        $stmt = $conn->query("DESCRIBE items");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = [
            'id', 'client_id', 'name', 'description', 'brand', 'model', 
            'serial_number', 'year', 'color', 'price_per_day', 'status', 
            'image_url', 'created_at', 'updated_at'
        ];
        
        $missing_columns = array_diff($required_columns, $columns);
        
        if (!empty($missing_columns)) {
            echo "<p style='color: orange;'>⚠️ Missing columns found: " . implode(', ', $missing_columns) . "</p>";
            
            // Add missing columns
            foreach ($missing_columns as $column) {
                switch ($column) {
                    case 'serial_number':
                        $conn->exec("ALTER TABLE items ADD COLUMN serial_number VARCHAR(100) UNIQUE");
                        echo "<p style='color: green;'>✅ Added serial_number column</p>";
                        break;
                    case 'year':
                        $conn->exec("ALTER TABLE items ADD COLUMN year INT");
                        echo "<p style='color: green;'>✅ Added year column</p>";
                        break;
                    case 'color':
                        $conn->exec("ALTER TABLE items ADD COLUMN color VARCHAR(30)");
                        echo "<p style='color: green;'>✅ Added color column</p>";
                        break;
                    case 'price_per_day':
                        $conn->exec("ALTER TABLE items ADD COLUMN price_per_day DECIMAL(10,2) NOT NULL DEFAULT 0");
                        echo "<p style='color: green;'>✅ Added price_per_day column</p>";
                        break;
                    case 'status':
                        $conn->exec("ALTER TABLE items ADD COLUMN status ENUM('available', 'rented', 'maintenance', 'inactive') DEFAULT 'available'");
                        echo "<p style='color: green;'>✅ Added status column</p>";
                        break;
                    case 'image_url':
                        $conn->exec("ALTER TABLE items ADD COLUMN image_url VARCHAR(255)");
                        echo "<p style='color: green;'>✅ Added image_url column</p>";
                        break;
                }
            }
        } else {
            echo "<p style='color: green;'>✅ All required columns exist in items table.</p>";
        }
    }
    
    // Check if rentals table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'rentals'");
    $rentals_exists = $stmt->rowCount() > 0;
    
    if (!$rentals_exists) {
        echo "<p style='color: orange;'>⚠️ Rentals table not found. Creating...</p>";
        
        // Create rentals table
        $sql = "CREATE TABLE IF NOT EXISTS rentals (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            item_id INT NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            customer_phone VARCHAR(20),
            customer_email VARCHAR(100),
            customer_address TEXT,
            rental_start_date DATE NOT NULL,
            rental_end_date DATE NOT NULL,
            actual_return_date DATE NULL,
            total_days INT NOT NULL,
            price_per_day DECIMAL(10,2) NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            deposit_amount DECIMAL(10,2) DEFAULT 0,
            status ENUM('active', 'returned', 'overdue', 'cancelled') DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
            FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ Rentals table created successfully!</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Rentals table already exists.</p>";
    }
    
    // Test the items table by trying to insert a sample record (then delete it)
    echo "<p style='color: blue;'>🧪 Testing items table...</p>";
    
    // Get a client ID for testing
    $stmt = $conn->query("SELECT id FROM clients LIMIT 1");
    $client = $stmt->fetch();
    
    if ($client) {
        $test_serial = 'TEST_' . time();
        
        try {
            // Try to insert a test record
            $stmt = $conn->prepare("INSERT INTO items (client_id, name, brand, serial_number, year, color, price_per_day) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$client->id, 'Test Item', 'Test Brand', $test_serial, 2023, 'Red', 50000]);
            
            echo "<p style='color: green;'>✅ Test insert successful!</p>";
            
            // Delete the test record
            $stmt = $conn->prepare("DELETE FROM items WHERE serial_number = ?");
            $stmt->execute([$test_serial]);
            
            echo "<p style='color: green;'>✅ Test record cleaned up!</p>";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Test insert failed: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✅ Database Fix Complete!</h3>";
    echo "<p>Your database has been updated and is ready to use.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='login.php'>Go to Login Page</a></li>";
    echo "<li><a href='test_comprehensive.php'>Run Comprehensive Test</a></li>";
    echo "<li><a href='add_item.php'>Try Adding an Item (after login)</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/config.php</p>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Fix Complete</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
    <!-- Content already echoed above -->
</body>
</html>

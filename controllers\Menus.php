<?php
/**
 * Menus Controller
 */

class Menus extends Controller {
    private $menuModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!$this->isLoggedIn()) {
            $this->redirect('auth/login');
        }
        
        // Check if user is superadmin
        if (!$this->hasRole('superadmin')) {
            $this->redirect('dashboard');
        }
        
        $this->menuModel = $this->model('Menu');
    }
    
    /**
     * Menus index
     */
    public function index() {
        // Get all menus
        $menus = $this->menuModel->getAllMenus();
        
        $data = [
            'title' => 'Menu Management',
            'menus' => $menus
        ];
        
        $this->view('menus/index', $data);
    }
    
    /**
     * Add menu
     */
    public function add() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'name' => trim($_POST['name']),
                'icon' => trim($_POST['icon']),
                'url' => trim($_POST['url']),
                'order_number' => trim($_POST['order_number']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate URL
            if (empty($data['url'])) {
                $data['url_err'] = 'Please enter URL';
            }
            
            // Validate order number
            if (empty($data['order_number'])) {
                $data['order_number_err'] = 'Please enter order number';
            } elseif (!is_numeric($data['order_number'])) {
                $data['order_number_err'] = 'Order number must be a number';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['url_err']) && 
                empty($data['order_number_err']) && empty($data['status_err'])) {
                
                // Create menu
                $menuId = $this->menuModel->createMenu($data);
                
                if ($menuId) {
                    // Menu created
                    // Set flash message
                    // Redirect to menus index
                    $this->redirect('menus');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Add Menu';
                
                $this->view('menus/add', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Add Menu',
                'name' => '',
                'icon' => '',
                'url' => '',
                'order_number' => '',
                'status' => 'active',
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('menus/add', $data);
        }
    }
    
    /**
     * Edit menu
     * 
     * @param int $id Menu ID
     */
    public function edit($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'icon' => trim($_POST['icon']),
                'url' => trim($_POST['url']),
                'order_number' => trim($_POST['order_number']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate URL
            if (empty($data['url'])) {
                $data['url_err'] = 'Please enter URL';
            }
            
            // Validate order number
            if (empty($data['order_number'])) {
                $data['order_number_err'] = 'Please enter order number';
            } elseif (!is_numeric($data['order_number'])) {
                $data['order_number_err'] = 'Order number must be a number';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['url_err']) && 
                empty($data['order_number_err']) && empty($data['status_err'])) {
                
                // Update menu
                if ($this->menuModel->updateMenu($data)) {
                    // Menu updated
                    // Set flash message
                    // Redirect to menus index
                    $this->redirect('menus');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Edit Menu';
                
                $this->view('menus/edit', $data);
            }
        } else {
            // Get menu
            $menu = $this->menuModel->getMenuById($id);
            
            // Check if menu exists
            if (!$menu) {
                $this->redirect('menus');
            }
            
            // Init data
            $data = [
                'title' => 'Edit Menu',
                'id' => $menu->id,
                'name' => $menu->name,
                'icon' => $menu->icon,
                'url' => $menu->url,
                'order_number' => $menu->order_number,
                'status' => $menu->status,
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('menus/edit', $data);
        }
    }
    
    /**
     * Delete menu
     * 
     * @param int $id Menu ID
     */
    public function delete($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete menu
            if ($this->menuModel->deleteMenu($id)) {
                // Menu deleted
                // Set flash message
                // Redirect to menus index
                $this->redirect('menus');
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('menus');
        }
    }
    
    /**
     * Submenus index
     * 
     * @param int $menuId Menu ID
     */
    public function submenus($menuId) {
        // Get menu
        $menu = $this->menuModel->getMenuById($menuId);
        
        // Check if menu exists
        if (!$menu) {
            $this->redirect('menus');
        }
        
        // Get submenus
        $submenus = $this->menuModel->getSubmenusByMenuId($menuId);
        
        $data = [
            'title' => 'Submenu Management',
            'menu' => $menu,
            'submenus' => $submenus
        ];
        
        $this->view('menus/submenus', $data);
    }
    
    /**
     * Add submenu
     * 
     * @param int $menuId Menu ID
     */
    public function addSubmenu($menuId) {
        // Get menu
        $menu = $this->menuModel->getMenuById($menuId);
        
        // Check if menu exists
        if (!$menu) {
            $this->redirect('menus');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'menu_id' => $menuId,
                'name' => trim($_POST['name']),
                'icon' => trim($_POST['icon']),
                'url' => trim($_POST['url']),
                'order_number' => trim($_POST['order_number']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate URL
            if (empty($data['url'])) {
                $data['url_err'] = 'Please enter URL';
            }
            
            // Validate order number
            if (empty($data['order_number'])) {
                $data['order_number_err'] = 'Please enter order number';
            } elseif (!is_numeric($data['order_number'])) {
                $data['order_number_err'] = 'Order number must be a number';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['url_err']) && 
                empty($data['order_number_err']) && empty($data['status_err'])) {
                
                // Create submenu
                $submenuId = $this->menuModel->createSubmenu($data);
                
                if ($submenuId) {
                    // Submenu created
                    // Set flash message
                    // Redirect to submenus index
                    $this->redirect('menus/submenus/' . $menuId);
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Add Submenu';
                $data['menu'] = $menu;
                
                $this->view('menus/add_submenu', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Add Submenu',
                'menu' => $menu,
                'menu_id' => $menuId,
                'name' => '',
                'icon' => '',
                'url' => '',
                'order_number' => '',
                'status' => 'active',
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('menus/add_submenu', $data);
        }
    }
    
    /**
     * Edit submenu
     * 
     * @param int $id Submenu ID
     */
    public function editSubmenu($id) {
        // Get submenu
        $this->db->query('SELECT * FROM submenus WHERE id = :id');
        $this->db->bind(':id', $id);
        $submenu = $this->db->single();
        
        // Check if submenu exists
        if (!$submenu) {
            $this->redirect('menus');
        }
        
        // Get menu
        $menu = $this->menuModel->getMenuById($submenu->menu_id);
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'id' => $id,
                'menu_id' => $submenu->menu_id,
                'name' => trim($_POST['name']),
                'icon' => trim($_POST['icon']),
                'url' => trim($_POST['url']),
                'order_number' => trim($_POST['order_number']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate URL
            if (empty($data['url'])) {
                $data['url_err'] = 'Please enter URL';
            }
            
            // Validate order number
            if (empty($data['order_number'])) {
                $data['order_number_err'] = 'Please enter order number';
            } elseif (!is_numeric($data['order_number'])) {
                $data['order_number_err'] = 'Order number must be a number';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['url_err']) && 
                empty($data['order_number_err']) && empty($data['status_err'])) {
                
                // Update submenu
                if ($this->menuModel->updateSubmenu($data)) {
                    // Submenu updated
                    // Set flash message
                    // Redirect to submenus index
                    $this->redirect('menus/submenus/' . $submenu->menu_id);
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Edit Submenu';
                $data['menu'] = $menu;
                
                $this->view('menus/edit_submenu', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Edit Submenu',
                'menu' => $menu,
                'id' => $submenu->id,
                'menu_id' => $submenu->menu_id,
                'name' => $submenu->name,
                'icon' => $submenu->icon,
                'url' => $submenu->url,
                'order_number' => $submenu->order_number,
                'status' => $submenu->status,
                'name_err' => '',
                'url_err' => '',
                'order_number_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('menus/edit_submenu', $data);
        }
    }
    
    /**
     * Delete submenu
     * 
     * @param int $id Submenu ID
     */
    public function deleteSubmenu($id) {
        // Get submenu
        $this->db->query('SELECT * FROM submenus WHERE id = :id');
        $this->db->bind(':id', $id);
        $submenu = $this->db->single();
        
        // Check if submenu exists
        if (!$submenu) {
            $this->redirect('menus');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete submenu
            if ($this->menuModel->deleteSubmenu($id)) {
                // Submenu deleted
                // Set flash message
                // Redirect to submenus index
                $this->redirect('menus/submenus/' . $submenu->menu_id);
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('menus');
        }
    }
}

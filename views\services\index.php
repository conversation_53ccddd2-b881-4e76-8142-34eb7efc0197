<?php require_once BASE_PATH . '/views/includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Service Management</h4>
                <a href="<?php echo APP_URL; ?>/services/add" class="btn btn-light">Add Service</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($data['services'] as $service) : ?>
                                <tr>
                                    <td><?php echo $service->id; ?></td>
                                    <td><?php echo $service->name; ?></td>
                                    <td><?php echo $service->description; ?></td>
                                    <td>
                                        <?php if($service->status == 'active') : ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else : ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo APP_URL; ?>/services/edit/<?php echo $service->id; ?>" class="btn btn-sm btn-primary">Edit</a>
                                        <form class="d-inline" action="<?php echo APP_URL; ?>/services/delete/<?php echo $service->id; ?>" method="post">
                                            <button type="submit" class="btn btn-sm btn-danger btn-delete">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once BASE_PATH . '/views/includes/footer.php'; ?>

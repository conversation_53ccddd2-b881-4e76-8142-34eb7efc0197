<?php
/**
 * Bootstrap file for SAAS Rental Service
 * This file initializes the application and loads all necessary components
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Define base path if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', __DIR__);
}

// Load configuration
if (file_exists(BASE_PATH . '/config/config.php')) {
    require_once BASE_PATH . '/config/config.php';
} else {
    die('Configuration file not found. Please check your installation.');
}

// Load autoloader
if (file_exists(BASE_PATH . '/config/autoload.php')) {
    require_once BASE_PATH . '/config/autoload.php';
} else {
    die('Autoloader not found. Please check your installation.');
}

// Load core classes
$core_classes = ['Database', 'Model', 'Controller'];
foreach ($core_classes as $class) {
    $file = BASE_PATH . '/core/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    } else {
        die("Core class {$class} not found. Please check your installation.");
    }
}

// Function to check if database is properly set up
function checkDatabaseSetup() {
    try {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
        $options = [
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
        ];
        
        $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
        
        // Check if essential tables exist
        $required_tables = ['users', 'clients', 'services', 'menus'];
        $stmt = $conn->query("SHOW TABLES");
        $existing_tables = [];
        
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $existing_tables[] = $row[0];
        }
        
        $missing_tables = array_diff($required_tables, $existing_tables);
        
        if (!empty($missing_tables)) {
            return false;
        }
        
        // Check if superadmin user exists
        $stmt = $conn->query('SELECT COUNT(*) FROM users WHERE username = "superadmin"');
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            return false;
        }
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to redirect to setup if database is not ready
function redirectToSetupIfNeeded() {
    $current_script = basename($_SERVER['PHP_SELF']);
    $setup_scripts = ['setup_database.php', 'test_comprehensive.php', 'test_db.php', 'init_db.php'];
    
    // Don't redirect if we're already on a setup script
    if (in_array($current_script, $setup_scripts)) {
        return;
    }
    
    if (!checkDatabaseSetup()) {
        header('Location: ' . APP_URL . '/setup_database.php');
        exit;
    }
}

// Function to handle errors gracefully
function handleError($errno, $errstr, $errfile, $errline) {
    if (DEBUG_MODE) {
        echo "<div class='alert alert-danger'>";
        echo "<strong>Error:</strong> [$errno] $errstr<br>";
        echo "<strong>File:</strong> $errfile<br>";
        echo "<strong>Line:</strong> $errline<br>";
        echo "</div>";
    } else {
        error_log("Error: [$errno] $errstr in $errfile on line $errline");
        echo "<div class='alert alert-danger'>An error occurred. Please try again later.</div>";
    }
}

// Function to handle exceptions
function handleException($exception) {
    if (DEBUG_MODE) {
        echo "<div class='alert alert-danger'>";
        echo "<strong>Uncaught Exception:</strong> " . $exception->getMessage() . "<br>";
        echo "<strong>File:</strong> " . $exception->getFile() . "<br>";
        echo "<strong>Line:</strong> " . $exception->getLine() . "<br>";
        echo "<strong>Stack Trace:</strong><br>";
        echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        error_log("Uncaught Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
        echo "<div class='alert alert-danger'>An error occurred. Please try again later.</div>";
    }
}

// Set error and exception handlers
set_error_handler('handleError');
set_exception_handler('handleException');

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Function to check user role
function hasRole($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

// Function to get current user info
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'username' => $_SESSION['user_username'],
        'email' => $_SESSION['user_email'],
        'name' => $_SESSION['user_name'],
        'role' => $_SESSION['user_role'],
        'client_id' => $_SESSION['client_id'] ?? null,
        'client_name' => $_SESSION['client_name'] ?? null,
        'rental_type' => $_SESSION['rental_type'] ?? null
    ];
}

// Function to set flash message
function setFlashMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

// Function to get and clear flash message
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = [
            'message' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type']
        ];
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        return $message;
    }
    return null;
}

// Function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Function to validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Function to generate CSRF token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Function to verify CSRF token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Initialize application
try {
    // Check database setup (only if not on setup pages)
    redirectToSetupIfNeeded();
    
    // Generate CSRF token for forms
    generateCSRFToken();
    
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die('Bootstrap Error: ' . $e->getMessage());
    } else {
        error_log('Bootstrap Error: ' . $e->getMessage());
        die('Application initialization failed. Please contact administrator.');
    }
}

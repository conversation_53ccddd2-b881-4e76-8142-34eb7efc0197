<?php
/**
 * Database initialization script
 * 
 * This script creates the database and tables for the SAAS Service Rental application.
 * Run this script once to set up the database.
 */

// Database configuration
$host = 'localhost';
$user = 'root';
$pass = '';
$dbname = 'saas_rental';

// Create connection
$conn = new mysqli($host, $user, $pass);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create database
$sql = "CREATE DATABASE IF NOT EXISTS $dbname";
if ($conn->query($sql) === TRUE) {
    echo "Database created successfully<br>";
} else {
    echo "Error creating database: " . $conn->error . "<br>";
}

// Select database
$conn->select_db($dbname);

// Read SQL file
$sql = file_get_contents('database.sql');

// Execute SQL script
if ($conn->multi_query($sql)) {
    echo "Database tables created successfully<br>";
    
    // Clear results
    while ($conn->more_results() && $conn->next_result()) {
        if ($result = $conn->store_result()) {
            $result->free();
        }
    }
} else {
    echo "Error creating tables: " . $conn->error . "<br>";
}

// Close connection
$conn->close();

echo "Database initialization completed.<br>";
echo "You can now access the application at <a href='index.php'>index.php</a>.<br>";
echo "Default superadmin credentials:<br>";
echo "Username: superadmin<br>";
echo "Password: password";

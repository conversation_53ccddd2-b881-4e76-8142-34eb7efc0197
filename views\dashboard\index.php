<?php require_once BASE_PATH . '/views/includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Dashboard</h4>
            </div>
            <div class="card-body">
                <h5>Welcome, <?php echo $_SESSION['user_name']; ?>!</h5>
                
                <?php if(isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'superadmin') : ?>
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card bg-info text-white mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">Clients</h5>
                                    <p class="card-text">Total: <?php echo count($data['clients']); ?></p>
                                </div>
                                <div class="card-footer d-flex align-items-center justify-content-between">
                                    <a class="small text-white stretched-link" href="<?php echo APP_URL; ?>/clients">View Details</a>
                                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">Services</h5>
                                    <p class="card-text">Total: <?php echo count($data['services']); ?></p>
                                </div>
                                <div class="card-footer d-flex align-items-center justify-content-between">
                                    <a class="small text-white stretched-link" href="<?php echo APP_URL; ?>/services">View Details</a>
                                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-white mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">Menus</h5>
                                    <p class="card-text">Total: <?php echo count($data['menus']); ?></p>
                                </div>
                                <div class="card-footer d-flex align-items-center justify-content-between">
                                    <a class="small text-white stretched-link" href="<?php echo APP_URL; ?>/menus">View Details</a>
                                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card shadow">
                                <div class="card-header">
                                    <h5 class="mb-0">Clients</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Name</th>
                                                    <th>Rental Type</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach($data['clients'] as $client) : ?>
                                                    <tr>
                                                        <td><?php echo $client->id; ?></td>
                                                        <td><?php echo $client->name; ?></td>
                                                        <td>
                                                            <?php if($client->rental_type == 'bicycle') : ?>
                                                                <span class="badge bg-primary">Bicycle</span>
                                                            <?php else : ?>
                                                                <span class="badge bg-danger">Motorcycle</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if($client->status == 'active') : ?>
                                                                <span class="badge bg-success">Active</span>
                                                            <?php else : ?>
                                                                <span class="badge bg-danger">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="<?php echo APP_URL; ?>/clients/services/<?php echo $client->id; ?>" class="btn btn-sm btn-info">Services</a>
                                                            <a href="<?php echo APP_URL; ?>/clients/menus/<?php echo $client->id; ?>" class="btn btn-sm btn-warning">Menus</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else : ?>
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5>Client Information</h5>
                                <p><strong>Name:</strong> <?php echo $data['client']->name; ?></p>
                                <p><strong>Rental Type:</strong> 
                                    <?php if($data['client']->rental_type == 'bicycle') : ?>
                                        <span class="badge bg-primary">Bicycle</span>
                                    <?php else : ?>
                                        <span class="badge bg-danger">Motorcycle</span>
                                    <?php endif; ?>
                                </p>
                                <p><strong>Status:</strong> 
                                    <?php if($data['client']->status == 'active') : ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else : ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card shadow">
                                <div class="card-header">
                                    <h5 class="mb-0">Available Services</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Name</th>
                                                    <th>Description</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach($data['services'] as $service) : ?>
                                                    <tr>
                                                        <td><?php echo $service->id; ?></td>
                                                        <td><?php echo $service->name; ?></td>
                                                        <td><?php echo $service->description; ?></td>
                                                        <td>
                                                            <?php if($service->status == 'active') : ?>
                                                                <span class="badge bg-success">Active</span>
                                                            <?php else : ?>
                                                                <span class="badge bg-danger">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once BASE_PATH . '/views/includes/footer.php'; ?>

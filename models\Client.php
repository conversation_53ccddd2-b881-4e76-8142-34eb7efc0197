<?php
/**
 * Client Model
 */

class Client extends Model {
    /**
     * Get client by ID
     * 
     * @param int $id Client ID
     * @return object|bool Client object or false if not found
     */
    public function getClientById($id) {
        $this->db->query('SELECT * FROM clients WHERE id = :id');
        $this->db->bind(':id', $id);
        
        $row = $this->db->single();
        
        return $row ? $row : false;
    }
    
    /**
     * Get all clients
     * 
     * @return array Array of client objects
     */
    public function getAllClients() {
        $this->db->query('SELECT * FROM clients ORDER BY id ASC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get clients by rental type
     * 
     * @param string $rentalType Rental type
     * @return array Array of client objects
     */
    public function getClientsByRentalType($rentalType) {
        $this->db->query('SELECT * FROM clients WHERE rental_type = :rental_type ORDER BY id ASC');
        $this->db->bind(':rental_type', $rentalType);
        
        return $this->db->resultSet();
    }
    
    /**
     * Create new client
     * 
     * @param array $data Client data
     * @return bool True on success, false on failure
     */
    public function createClient($data) {
        $this->db->query('INSERT INTO clients (name, description, rental_type, status) 
                          VALUES (:name, :description, :rental_type, :status)');
        
        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':rental_type', $data['rental_type']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update client
     * 
     * @param array $data Client data
     * @return bool True on success, false on failure
     */
    public function updateClient($data) {
        $this->db->query('UPDATE clients SET name = :name, description = :description, 
                          rental_type = :rental_type, status = :status 
                          WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':rental_type', $data['rental_type']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Delete client
     * 
     * @param int $id Client ID
     * @return bool True on success, false on failure
     */
    public function deleteClient($id) {
        $this->db->query('DELETE FROM clients WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $id);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
}

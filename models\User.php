<?php
/**
 * User Model
 */

class User extends Model {
    /**
     * Get user by ID
     * 
     * @param int $id User ID
     * @return object|bool User object or false if not found
     */
    public function getUserById($id) {
        $this->db->query('SELECT * FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        
        $row = $this->db->single();
        
        return $row ? $row : false;
    }
    
    /**
     * Get user by username
     * 
     * @param string $username Username
     * @return object|bool User object or false if not found
     */
    public function getUserByUsername($username) {
        $this->db->query('SELECT * FROM users WHERE username = :username');
        $this->db->bind(':username', $username);
        
        $row = $this->db->single();
        
        return $row ? $row : false;
    }
    
    /**
     * Get all users
     * 
     * @return array Array of user objects
     */
    public function getAllUsers() {
        $this->db->query('SELECT * FROM users ORDER BY id ASC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get users by role
     * 
     * @param string $role User role
     * @return array Array of user objects
     */
    public function getUsersByRole($role) {
        $this->db->query('SELECT * FROM users WHERE role = :role ORDER BY id ASC');
        $this->db->bind(':role', $role);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get users by client ID
     * 
     * @param int $clientId Client ID
     * @return array Array of user objects
     */
    public function getUsersByClientId($clientId) {
        $this->db->query('SELECT * FROM users WHERE client_id = :client_id ORDER BY id ASC');
        $this->db->bind(':client_id', $clientId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Create new user
     * 
     * @param array $data User data
     * @return bool True on success, false on failure
     */
    public function createUser($data) {
        $this->db->query('INSERT INTO users (username, password, email, full_name, role, client_id) 
                          VALUES (:username, :password, :email, :full_name, :role, :client_id)');
        
        // Bind values
        $this->db->bind(':username', $data['username']);
        $this->db->bind(':password', $data['password']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':full_name', $data['full_name']);
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':client_id', $data['client_id']);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Update user
     * 
     * @param array $data User data
     * @return bool True on success, false on failure
     */
    public function updateUser($data) {
        $this->db->query('UPDATE users SET username = :username, email = :email, 
                          full_name = :full_name, role = :role, client_id = :client_id 
                          WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':username', $data['username']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':full_name', $data['full_name']);
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':client_id', $data['client_id']);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Update user password
     * 
     * @param int $id User ID
     * @param string $password Hashed password
     * @return bool True on success, false on failure
     */
    public function updatePassword($id, $password) {
        $this->db->query('UPDATE users SET password = :password WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $id);
        $this->db->bind(':password', $password);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Delete user
     * 
     * @param int $id User ID
     * @return bool True on success, false on failure
     */
    public function deleteUser($id) {
        $this->db->query('DELETE FROM users WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $id);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
}

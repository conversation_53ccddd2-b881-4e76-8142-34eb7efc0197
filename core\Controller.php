<?php
/**
 * Base Controller class
 */

class Controller {
    /**
     * Load model
     * 
     * @param string $model Model name
     * @return object Model instance
     */
    protected function model($model) {
        // Check if model file exists
        if (file_exists(BASE_PATH . '/models/' . $model . '.php')) {
            require_once BASE_PATH . '/models/' . $model . '.php';
            return new $model();
        }
        
        return null;
    }
    
    /**
     * Load view
     * 
     * @param string $view View name
     * @param array $data Data to pass to the view
     * @return void
     */
    protected function view($view, $data = []) {
        // Check if view file exists
        if (file_exists(BASE_PATH . '/views/' . $view . '.php')) {
            require_once BASE_PATH . '/views/' . $view . '.php';
        } else {
            // View not found
            die('View not found: ' . $view);
        }
    }
    
    /**
     * Redirect to another page
     * 
     * @param string $url URL to redirect to
     * @return void
     */
    protected function redirect($url) {
        header('Location: ' . APP_URL . '/' . $url);
        exit;
    }
    
    /**
     * Check if user is logged in
     * 
     * @return bool True if logged in, false otherwise
     */
    protected function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }
    
    /**
     * Check if user has specific role
     * 
     * @param string $role Role to check
     * @return bool True if user has role, false otherwise
     */
    protected function hasRole($role) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        return $_SESSION['user_role'] === $role;
    }
    
    /**
     * Get current user's client ID
     * 
     * @return int|null Client ID or null if not applicable
     */
    protected function getClientId() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return $_SESSION['client_id'] ?? null;
    }
}

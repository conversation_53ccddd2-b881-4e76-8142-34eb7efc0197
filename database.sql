-- Database for SAAS Service Rental

-- Create database
CREATE DATABASE IF NOT EXISTS saas_rental;
USE saas_rental;

-- Users table (Superadmin and Admin)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('superadmin', 'admin') NOT NULL,
    client_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Clients table (Client A and Client B)
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(100) NOT NULL,
    description TEXT,
    rental_type ENUM('bicycle', 'motorcycle') NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Client-Service mapping
CREATE TABLE IF NOT EXISTS client_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    service_id INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY (client_id, service_id)
);

-- Menus table
CREATE TABLE IF NOT EXISTS menus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    icon VARCHAR(50),
    url VARCHAR(255),
    order_number INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Submenus table
CREATE TABLE IF NOT EXISTS submenus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    menu_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    icon VARCHAR(50),
    url VARCHAR(255) NOT NULL,
    order_number INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (menu_id) REFERENCES menus(id) ON DELETE CASCADE
);

-- Client-Menu mapping
CREATE TABLE IF NOT EXISTS client_menus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    menu_id INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (menu_id) REFERENCES menus(id) ON DELETE CASCADE,
    UNIQUE KEY (client_id, menu_id)
);

-- Client-Submenu mapping
CREATE TABLE IF NOT EXISTS client_submenus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    submenu_id INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (submenu_id) REFERENCES submenus(id) ON DELETE CASCADE,
    UNIQUE KEY (client_id, submenu_id)
);

-- Insert default superadmin user
INSERT INTO users (username, password, email, full_name, role)
VALUES ('superadmin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Super Admin', 'superadmin');

-- Insert default clients
INSERT INTO clients (name, description, rental_type)
VALUES 
('Client A', 'Bicycle Rental Service', 'bicycle'),
('Client B', 'Motorcycle Rental Service', 'motorcycle');

-- Insert default services
INSERT INTO services (name, description)
VALUES 
('Rental Management', 'Manage rental items, bookings, and returns'),
('Customer Management', 'Manage customer information and history'),
('Reporting', 'Generate and view reports'),
('Maintenance Tracking', 'Track maintenance schedules and history');

-- Insert default menus
INSERT INTO menus (name, icon, url, order_number)
VALUES 
('Dashboard', 'fas fa-tachometer-alt', 'dashboard', 1),
('User Management', 'fas fa-users', 'users', 2),
('Clients', 'fas fa-building', 'clients', 3),
('Services', 'fas fa-cogs', 'services', 4),
('Menus', 'fas fa-list', 'menus', 5),
('Bicycles', 'fas fa-bicycle', 'bicycles', 6),
('Motorcycles', 'fas fa-motorcycle', 'motorcycles', 7),
('Rentals', 'fas fa-calendar-check', 'rentals', 8),
('Customers', 'fas fa-user-friends', 'customers', 9),
('Reports', 'fas fa-chart-bar', 'reports', 10),
('Settings', 'fas fa-cog', 'settings', 11);

-- Assign menus to clients
INSERT INTO client_menus (client_id, menu_id)
VALUES 
-- Common menus for both clients
(1, 1), -- Dashboard for Client A
(2, 1), -- Dashboard for Client B
(1, 9), -- Customers for Client A
(2, 9), -- Customers for Client B
(1, 8), -- Rentals for Client A
(2, 8), -- Rentals for Client B
(1, 10), -- Reports for Client A
(2, 10), -- Reports for Client B
(1, 11), -- Settings for Client A
(2, 11), -- Settings for Client B

-- Client-specific menus
(1, 6), -- Bicycles only for Client A
(2, 7); -- Motorcycles only for Client B

-- Assign services to clients
INSERT INTO client_services (client_id, service_id)
VALUES 
(1, 1), -- Rental Management for Client A
(1, 2), -- Customer Management for Client A
(1, 3), -- Reporting for Client A
(2, 1), -- Rental Management for Client B
(2, 2), -- Customer Management for Client B
(2, 3), -- Reporting for Client B
(2, 4); -- Maintenance Tracking only for Client B

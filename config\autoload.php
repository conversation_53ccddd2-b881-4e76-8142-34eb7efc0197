<?php
/**
 * Autoloader for classes
 */

spl_autoload_register(function ($class) {
    // Convert namespace to file path
    $class = str_replace('\\', '/', $class);
    
    // Check for controllers
    if (file_exists(BASE_PATH . '/controllers/' . $class . '.php')) {
        require_once BASE_PATH . '/controllers/' . $class . '.php';
        return;
    }
    
    // Check for models
    if (file_exists(BASE_PATH . '/models/' . $class . '.php')) {
        require_once BASE_PATH . '/models/' . $class . '.php';
        return;
    }
    
    // Check for core classes
    if (file_exists(BASE_PATH . '/core/' . $class . '.php')) {
        require_once BASE_PATH . '/core/' . $class . '.php';
        return;
    }
    
    // Check for helpers
    if (file_exists(BASE_PATH . '/helpers/' . $class . '.php')) {
        require_once BASE_PATH . '/helpers/' . $class . '.php';
        return;
    }
});

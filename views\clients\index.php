<?php require_once BASE_PATH . '/views/includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Client Management</h4>
                <a href="<?php echo APP_URL; ?>/clients/add" class="btn btn-light">Add Client</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Rental Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($data['clients'] as $client) : ?>
                                <tr>
                                    <td><?php echo $client->id; ?></td>
                                    <td><?php echo $client->name; ?></td>
                                    <td><?php echo $client->description; ?></td>
                                    <td>
                                        <?php if($client->rental_type == 'bicycle') : ?>
                                            <span class="badge bg-primary">Bicycle</span>
                                        <?php else : ?>
                                            <span class="badge bg-danger">Motorcycle</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($client->status == 'active') : ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else : ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo APP_URL; ?>/clients/edit/<?php echo $client->id; ?>" class="btn btn-sm btn-primary">Edit</a>
                                        <a href="<?php echo APP_URL; ?>/clients/services/<?php echo $client->id; ?>" class="btn btn-sm btn-info">Services</a>
                                        <a href="<?php echo APP_URL; ?>/clients/menus/<?php echo $client->id; ?>" class="btn btn-sm btn-warning">Menus</a>
                                        <form class="d-inline" action="<?php echo APP_URL; ?>/clients/delete/<?php echo $client->id; ?>" method="post">
                                            <button type="submit" class="btn btn-sm btn-danger btn-delete">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once BASE_PATH . '/views/includes/footer.php'; ?>

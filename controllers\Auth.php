<?php
/**
 * Auth Controller
 */

class Auth extends Controller {
    private $userModel;
    private $clientModel;

    /**
     * Constructor
     */
    public function __construct() {
        $this->userModel = $this->model('User');
        $this->clientModel = $this->model('Client');
    }

    /**
     * Login page
     */
    public function login() {
        // Check if already logged in
        if ($this->isLoggedIn()) {
            // Simply redirect to dashboard without any checks
            // This is safe because we're checking isLoggedIn() first
            $this->redirect('dashboard');
            return;
        }

        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form

            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);

            // Init data
            $data = [
                'username' => trim($_POST['username']),
                'password' => trim($_POST['password']),
                'username_err' => '',
                'password_err' => ''
            ];

            // Validate username
            if (empty($data['username'])) {
                $data['username_err'] = 'Please enter username';
            }

            // Validate password
            if (empty($data['password'])) {
                $data['password_err'] = 'Please enter password';
            }

            // Check for user/email
            $user = $this->userModel->getUserByUsername($data['username']);

            if (!$user) {
                // User not found
                $data['username_err'] = 'No user found';
            }

            // Make sure errors are empty
            if (empty($data['username_err']) && empty($data['password_err'])) {
                // Validated
                // Check and set logged in user
                if (password_verify($data['password'], $user->password)) {
                    // Create session
                    $_SESSION['user_id'] = $user->id;
                    $_SESSION['user_username'] = $user->username;
                    $_SESSION['user_email'] = $user->email;
                    $_SESSION['user_name'] = $user->full_name;
                    $_SESSION['user_role'] = $user->role;

                    // If admin, set client ID
                    if ($user->role == 'admin' && $user->client_id) {
                        $_SESSION['client_id'] = $user->client_id;

                        // Get client info
                        $client = $this->clientModel->getClientById($user->client_id);
                        if ($client) {
                            $_SESSION['client_name'] = $client->name;
                            $_SESSION['rental_type'] = $client->rental_type;
                        }
                    }

                    $this->redirect('dashboard');
                } else {
                    $data['password_err'] = 'Password incorrect';
                    $this->view('auth/login', $data);
                }
            } else {
                // Load view with errors
                $this->view('auth/login', $data);
            }
        } else {
            // Init data
            $data = [
                'username' => '',
                'password' => '',
                'username_err' => '',
                'password_err' => ''
            ];

            // Load view
            $this->view('auth/login', $data);
        }
    }

    /**
     * Logout
     */
    public function logout() {
        // Unset session variables
        unset($_SESSION['user_id']);
        unset($_SESSION['user_username']);
        unset($_SESSION['user_email']);
        unset($_SESSION['user_name']);
        unset($_SESSION['user_role']);
        unset($_SESSION['client_id']);
        unset($_SESSION['client_name']);
        unset($_SESSION['rental_type']);

        // Destroy session
        session_destroy();

        // Redirect to login
        $this->redirect('auth/login');
    }
}

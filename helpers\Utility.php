<?php
/**
 * Utility Helper Class
 */

class Utility {
    /**
     * Set flash message
     * 
     * @param string $message Message to display
     * @param string $type Message type (success, danger, warning, info)
     * @return void
     */
    public static function setFlashMessage($message, $type = 'success') {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
    
    /**
     * Redirect to URL
     * 
     * @param string $url URL to redirect to
     * @return void
     */
    public static function redirect($url) {
        header('Location: ' . APP_URL . '/' . $url);
        exit;
    }
    
    /**
     * Sanitize input
     * 
     * @param mixed $input Input to sanitize
     * @return mixed Sanitized input
     */
    public static function sanitize($input) {
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = self::sanitize($value);
            }
            return $input;
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Generate random string
     * 
     * @param int $length Length of string
     * @return string Random string
     */
    public static function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
    
    /**
     * Format date
     * 
     * @param string $date Date to format
     * @param string $format Format to use
     * @return string Formatted date
     */
    public static function formatDate($date, $format = 'd M Y') {
        return date($format, strtotime($date));
    }
    
    /**
     * Format time
     * 
     * @param string $time Time to format
     * @param string $format Format to use
     * @return string Formatted time
     */
    public static function formatTime($time, $format = 'H:i:s') {
        return date($format, strtotime($time));
    }
    
    /**
     * Format datetime
     * 
     * @param string $datetime Datetime to format
     * @param string $format Format to use
     * @return string Formatted datetime
     */
    public static function formatDateTime($datetime, $format = 'd M Y H:i:s') {
        return date($format, strtotime($datetime));
    }
    
    /**
     * Format currency
     * 
     * @param float $amount Amount to format
     * @param string $currency Currency symbol
     * @return string Formatted currency
     */
    public static function formatCurrency($amount, $currency = 'Rp') {
        return $currency . ' ' . number_format($amount, 0, ',', '.');
    }
    
    /**
     * Get client rental type name
     * 
     * @param string $rentalType Rental type
     * @return string Rental type name
     */
    public static function getRentalTypeName($rentalType) {
        switch ($rentalType) {
            case 'bicycle':
                return 'Bicycle';
            case 'motorcycle':
                return 'Motorcycle';
            default:
                return 'Unknown';
        }
    }
    
    /**
     * Get status badge
     * 
     * @param string $status Status
     * @return string Status badge HTML
     */
    public static function getStatusBadge($status) {
        switch ($status) {
            case 'active':
                return '<span class="badge bg-success">Active</span>';
            case 'inactive':
                return '<span class="badge bg-danger">Inactive</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }
    
    /**
     * Get role badge
     * 
     * @param string $role Role
     * @return string Role badge HTML
     */
    public static function getRoleBadge($role) {
        switch ($role) {
            case 'superadmin':
                return '<span class="badge bg-danger">Superadmin</span>';
            case 'admin':
                return '<span class="badge bg-primary">Admin</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }
}

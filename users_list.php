<?php
/**
 * Users List Page (for Superadmin)
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check if user is logged in and is superadmin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'superadmin') {
    header('Location: login.php');
    exit;
}

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Get all users with client information
    $stmt = $conn->query('SELECT u.*, c.name as client_name, c.rental_type 
                         FROM users u 
                         LEFT JOIN clients c ON u.client_id = c.id 
                         ORDER BY u.role DESC, u.created_at ASC');
    $users = $stmt->fetchAll();
    
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users Management - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard_direct.php"><?php echo APP_NAME; ?></a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard_direct.php">Dashboard</a>
                <a class="nav-link" href="add_admin.php">Add Admin</a>
                <a class="nav-link" href="logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Users Management</h4>
                        <a href="add_admin.php" class="btn btn-light">
                            <i class="fas fa-plus"></i> Add Admin User
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Client</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user) : ?>
                                        <tr>
                                            <td><?php echo $user->id; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($user->username); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($user->full_name); ?></td>
                                            <td><?php echo htmlspecialchars($user->email); ?></td>
                                            <td>
                                                <?php if ($user->role === 'superadmin') : ?>
                                                    <span class="badge bg-danger">Superadmin</span>
                                                <?php else : ?>
                                                    <span class="badge bg-primary">Admin</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user->client_name) : ?>
                                                    <?php echo htmlspecialchars($user->client_name); ?>
                                                    <br><small class="text-muted"><?php echo ucfirst($user->rental_type); ?></small>
                                                <?php else : ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('d M Y', strtotime($user->created_at)); ?></small>
                                            </td>
                                            <td>
                                                <?php if ($user->role !== 'superadmin' || $user->id != $_SESSION['user_id']) : ?>
                                                    <a href="edit_user.php?id=<?php echo $user->id; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user->id != $_SESSION['user_id']) : ?>
                                                        <a href="delete_user.php?id=<?php echo $user->id; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this user?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                <?php else : ?>
                                                    <span class="text-muted">Current User</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

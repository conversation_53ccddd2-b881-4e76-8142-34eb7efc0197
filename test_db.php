<?php
/**
 * Database connection test script
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'saas_rental');
define('DB_USER', 'root');
define('DB_PASS', '');

// Test connection
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    echo "Database connection successful!<br>";
    
    // Test query
    $stmt = $conn->query('SELECT * FROM users WHERE username = "superadmin"');
    $user = $stmt->fetch();
    
    if ($user) {
        echo "Superadmin user found!<br>";
        echo "Username: " . $user->username . "<br>";
        echo "Email: " . $user->email . "<br>";
        echo "Role: " . $user->role . "<br>";
        
        // Test password
        echo "Password hash: " . $user->password . "<br>";
        
        // The default password in the database is 'password'
        $testPassword = 'password';
        if (password_verify($testPassword, $user->password)) {
            echo "Password verification successful! The password is: " . $testPassword . "<br>";
        } else {
            echo "Password verification failed. The hash doesn't match the password 'password'.<br>";
        }
    } else {
        echo "Superadmin user not found!<br>";
    }
} catch (PDOException $e) {
    echo "Database Connection Error: " . $e->getMessage() . "<br>";
}
?>

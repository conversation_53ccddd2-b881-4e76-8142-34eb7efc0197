<?php
/**
 * Reset superadmin password script
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'saas_rental');
define('DB_USER', 'root');
define('DB_PASS', '');

// New password
$newPassword = 'password';
$hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    echo "Database connection successful!<br>";
    
    // Update superadmin password
    $stmt = $conn->prepare('UPDATE users SET password = :password WHERE username = "superadmin"');
    $stmt->bindParam(':password', $hashedPassword);
    
    if ($stmt->execute()) {
        echo "Superadmin password reset successfully!<br>";
        echo "New password: " . $newPassword . "<br>";
        echo "Hashed password: " . $hashedPassword . "<br>";
    } else {
        echo "Failed to reset superadmin password!<br>";
    }
} catch (PDOException $e) {
    echo "Database Error: " . $e->getMessage() . "<br>";
}
?>

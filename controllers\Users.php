<?php
/**
 * Users Controller
 */

class Users extends Controller {
    private $userModel;
    private $clientModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!$this->isLoggedIn()) {
            $this->redirect('auth/login');
        }
        
        // Check if user is superadmin
        if (!$this->hasRole('superadmin')) {
            $this->redirect('dashboard');
        }
        
        $this->userModel = $this->model('User');
        $this->clientModel = $this->model('Client');
    }
    
    /**
     * Users index
     */
    public function index() {
        // Get all users
        $users = $this->userModel->getAllUsers();
        $clients = $this->clientModel->getAllClients();
        
        $data = [
            'title' => 'User Management',
            'users' => $users,
            'clients' => $clients
        ];
        
        $this->view('users/index', $data);
    }
    
    /**
     * Add user
     */
    public function add() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'username' => trim($_POST['username']),
                'email' => trim($_POST['email']),
                'password' => trim($_POST['password']),
                'confirm_password' => trim($_POST['confirm_password']),
                'full_name' => trim($_POST['full_name']),
                'role' => trim($_POST['role']),
                'client_id' => $_POST['client_id'] ? trim($_POST['client_id']) : null,
                'username_err' => '',
                'email_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'full_name_err' => '',
                'role_err' => '',
                'client_id_err' => ''
            ];
            
            // Validate username
            if (empty($data['username'])) {
                $data['username_err'] = 'Please enter username';
            } else {
                // Check if username exists
                if ($this->userModel->getUserByUsername($data['username'])) {
                    $data['username_err'] = 'Username is already taken';
                }
            }
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter email';
            }
            
            // Validate password
            if (empty($data['password'])) {
                $data['password_err'] = 'Please enter password';
            } elseif (strlen($data['password']) < 6) {
                $data['password_err'] = 'Password must be at least 6 characters';
            }
            
            // Validate confirm password
            if (empty($data['confirm_password'])) {
                $data['confirm_password_err'] = 'Please confirm password';
            } else {
                if ($data['password'] != $data['confirm_password']) {
                    $data['confirm_password_err'] = 'Passwords do not match';
                }
            }
            
            // Validate full name
            if (empty($data['full_name'])) {
                $data['full_name_err'] = 'Please enter full name';
            }
            
            // Validate role
            if (empty($data['role'])) {
                $data['role_err'] = 'Please select role';
            }
            
            // Validate client ID (if admin)
            if ($data['role'] == 'admin' && empty($data['client_id'])) {
                $data['client_id_err'] = 'Please select client';
            }
            
            // Make sure errors are empty
            if (empty($data['username_err']) && empty($data['email_err']) && 
                empty($data['password_err']) && empty($data['confirm_password_err']) && 
                empty($data['full_name_err']) && empty($data['role_err']) && 
                empty($data['client_id_err'])) {
                
                // Hash password
                $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
                
                // Create user
                if ($this->userModel->createUser($data)) {
                    // User created
                    // Set flash message
                    // Redirect to users index
                    $this->redirect('users');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $clients = $this->clientModel->getAllClients();
                $data['clients'] = $clients;
                $data['title'] = 'Add User';
                
                $this->view('users/add', $data);
            }
        } else {
            // Init data
            $clients = $this->clientModel->getAllClients();
            
            $data = [
                'title' => 'Add User',
                'username' => '',
                'email' => '',
                'password' => '',
                'confirm_password' => '',
                'full_name' => '',
                'role' => '',
                'client_id' => '',
                'clients' => $clients,
                'username_err' => '',
                'email_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'full_name_err' => '',
                'role_err' => '',
                'client_id_err' => ''
            ];
            
            // Load view
            $this->view('users/add', $data);
        }
    }
    
    /**
     * Edit user
     * 
     * @param int $id User ID
     */
    public function edit($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'id' => $id,
                'username' => trim($_POST['username']),
                'email' => trim($_POST['email']),
                'full_name' => trim($_POST['full_name']),
                'role' => trim($_POST['role']),
                'client_id' => $_POST['client_id'] ? trim($_POST['client_id']) : null,
                'username_err' => '',
                'email_err' => '',
                'full_name_err' => '',
                'role_err' => '',
                'client_id_err' => ''
            ];
            
            // Validate username
            if (empty($data['username'])) {
                $data['username_err'] = 'Please enter username';
            }
            
            // Validate email
            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter email';
            }
            
            // Validate full name
            if (empty($data['full_name'])) {
                $data['full_name_err'] = 'Please enter full name';
            }
            
            // Validate role
            if (empty($data['role'])) {
                $data['role_err'] = 'Please select role';
            }
            
            // Validate client ID (if admin)
            if ($data['role'] == 'admin' && empty($data['client_id'])) {
                $data['client_id_err'] = 'Please select client';
            }
            
            // Make sure errors are empty
            if (empty($data['username_err']) && empty($data['email_err']) && 
                empty($data['full_name_err']) && empty($data['role_err']) && 
                empty($data['client_id_err'])) {
                
                // Update user
                if ($this->userModel->updateUser($data)) {
                    // User updated
                    // Set flash message
                    // Redirect to users index
                    $this->redirect('users');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $clients = $this->clientModel->getAllClients();
                $data['clients'] = $clients;
                $data['title'] = 'Edit User';
                
                $this->view('users/edit', $data);
            }
        } else {
            // Get user
            $user = $this->userModel->getUserById($id);
            
            // Check if user exists
            if (!$user) {
                $this->redirect('users');
            }
            
            // Get clients
            $clients = $this->clientModel->getAllClients();
            
            // Init data
            $data = [
                'title' => 'Edit User',
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'full_name' => $user->full_name,
                'role' => $user->role,
                'client_id' => $user->client_id,
                'clients' => $clients,
                'username_err' => '',
                'email_err' => '',
                'full_name_err' => '',
                'role_err' => '',
                'client_id_err' => ''
            ];
            
            // Load view
            $this->view('users/edit', $data);
        }
    }
    
    /**
     * Delete user
     * 
     * @param int $id User ID
     */
    public function delete($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete user
            if ($this->userModel->deleteUser($id)) {
                // User deleted
                // Set flash message
                // Redirect to users index
                $this->redirect('users');
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('users');
        }
    }
}

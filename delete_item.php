<?php
/**
 * Delete Item Page (for Admin users)
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Get user information
$user_id = $_SESSION['user_id'];
$client_id = $_SESSION['client_id'];
$rental_type = $_SESSION['rental_type'];

// Get item ID from URL
$item_id = $_GET['id'] ?? 0;

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Verify that the item belongs to this client
    $stmt = $conn->prepare('SELECT * FROM items WHERE id = :id AND client_id = :client_id');
    $stmt->bindParam(':id', $item_id);
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $item = $stmt->fetch();
    
    if (!$item) {
        // Item not found or doesn't belong to this client
        header('Location: items.php');
        exit;
    }
    
    // Check if item is currently rented
    if ($item->status === 'rented') {
        // Cannot delete rented items
        $_SESSION['error_message'] = 'Cannot delete item that is currently rented.';
        header('Location: items.php');
        exit;
    }
    
    // Delete the item
    $stmt = $conn->prepare('DELETE FROM items WHERE id = :id AND client_id = :client_id');
    $stmt->bindParam(':id', $item_id);
    $stmt->bindParam(':client_id', $client_id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = ucfirst($rental_type) . ' "' . $item->name . '" has been deleted successfully.';
    } else {
        $_SESSION['error_message'] = 'Failed to delete the item.';
    }
    
} catch (PDOException $e) {
    $_SESSION['error_message'] = 'Database error: ' . $e->getMessage();
}

// Redirect back to items page
header('Location: items.php');
exit;
?>

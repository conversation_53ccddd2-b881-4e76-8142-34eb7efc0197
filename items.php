<?php
/**
 * Items Management Page (for Admin users)
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Get user information
$user_id = $_SESSION['user_id'];
$client_id = $_SESSION['client_id'];
$client_name = $_SESSION['client_name'];
$rental_type = $_SESSION['rental_type'];

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Get items for this client
    $stmt = $conn->prepare('SELECT * FROM items WHERE client_id = :client_id ORDER BY created_at DESC');
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $items = $stmt->fetchAll();
    
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ucfirst($rental_type); ?> Management - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .item-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
        }
        .status-badge {
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard_direct.php"><?php echo APP_NAME; ?></a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard_direct.php">Dashboard</a>
                <a class="nav-link" href="items.php" class="active"><?php echo ucfirst($rental_type); ?>s</a>
                <a class="nav-link" href="logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><?php echo ucfirst($rental_type); ?> Management - <?php echo $client_name; ?></h4>
                        <a href="add_item.php" class="btn btn-light">
                            <i class="fas fa-plus"></i> Add <?php echo ucfirst($rental_type); ?>
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (isset($_SESSION['success_message'])) : ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $_SESSION['success_message']; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            <?php unset($_SESSION['success_message']); ?>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['error_message'])) : ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $_SESSION['error_message']; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            <?php unset($_SESSION['error_message']); ?>
                        <?php endif; ?>

                        <?php if (empty($items)) : ?>
                            <div class="text-center py-5">
                                <i class="fas fa-<?php echo $rental_type === 'bicycle' ? 'bicycle' : 'motorcycle'; ?> fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No <?php echo $rental_type; ?>s found</h5>
                                <p class="text-muted">Start by adding your first <?php echo $rental_type; ?> to the inventory.</p>
                                <a href="add_item.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add <?php echo ucfirst($rental_type); ?>
                                </a>
                            </div>
                        <?php else : ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Image</th>
                                            <th>Name</th>
                                            <th>Brand/Model</th>
                                            <th>Serial No.</th>
                                            <th>Year</th>
                                            <th>Color</th>
                                            <th>Price/Day</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($items as $item) : ?>
                                            <tr>
                                                <td>
                                                    <?php if (!empty($item->image_url)) : ?>
                                                        <img src="<?php echo $item->image_url; ?>" alt="<?php echo $item->name; ?>" class="item-image">
                                                    <?php else : ?>
                                                        <div class="item-image bg-light d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-<?php echo $rental_type === 'bicycle' ? 'bicycle' : 'motorcycle'; ?> text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($item->name); ?></strong>
                                                    <?php if (!empty($item->description)) : ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($item->description); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($item->brand); ?>
                                                    <?php if (!empty($item->model)) : ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($item->model); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <code><?php echo htmlspecialchars($item->serial_number); ?></code>
                                                </td>
                                                <td><?php echo $item->year; ?></td>
                                                <td><?php echo htmlspecialchars($item->color); ?></td>
                                                <td>Rp <?php echo number_format($item->price_per_day, 0, ',', '.'); ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($item->status) {
                                                        case 'available':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'rented':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'maintenance':
                                                            $status_class = 'bg-info';
                                                            break;
                                                        case 'inactive':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?> status-badge">
                                                        <?php echo ucfirst($item->status); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="edit_item.php?id=<?php echo $item->id; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete_item.php?id=<?php echo $item->id; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this item?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

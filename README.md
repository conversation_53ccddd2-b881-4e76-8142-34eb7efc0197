# SAAS Service Rental

A Software as a Service (SAAS) rental management system that supports multiple clients with different rental types.

## Features

- Multi-tenant architecture supporting different client types
- Role-based access control (Superadmin and Admin roles)
- Dynamic menu system based on client type
- Client A: Bicycle rental service
- Client B: Motorcycle rental service
- Service management for each client
- Menu and submenu management

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite enabled
- XAMPP (recommended for local development)

## Installation

1. Clone the repository to your XAMPP htdocs directory:
   ```
   git clone https://github.com/yourusername/saas-rental.git
   ```

2. Create a MySQL database named `saas_rental`.

3. Import the database schema from `database.sql`:
   ```
   mysql -u root -p saas_rental < database.sql
   ```

4. Configure the application:
   - Update database credentials in `config/config.php` if needed
   - Update the base URL in `config/config.php` if needed

5. Access the application:
   - URL: http://localhost/ProjectUAS/
   - Default superadmin credentials:
     - Username: superadmin
     - Password: password

## Usage

### Superadmin

As a superadmin, you can:
- Manage users (create, edit, delete)
- Manage clients (create, edit, delete)
- Manage services (create, edit, delete)
- Manage menus and submenus (create, edit, delete)
- Assign services to clients
- Assign menus to clients

### Admin (Client A - Bicycle Rental)

As an admin for Client A, you can:
- Manage bicycles
- Manage rentals
- Manage customers
- View reports
- Access settings

### Admin (Client B - Motorcycle Rental)

As an admin for Client B, you can:
- Manage motorcycles
- Manage rentals
- Manage customers
- View reports
- Access settings
- Track maintenance (exclusive to motorcycle rental)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

Your Name

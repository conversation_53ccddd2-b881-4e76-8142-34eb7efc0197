<?php
/**
 * Clients Controller
 */

class Clients extends Controller {
    private $clientModel;
    private $serviceModel;
    private $menuModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!$this->isLoggedIn()) {
            $this->redirect('auth/login');
        }
        
        // Check if user is superadmin
        if (!$this->hasRole('superadmin')) {
            $this->redirect('dashboard');
        }
        
        $this->clientModel = $this->model('Client');
        $this->serviceModel = $this->model('Service');
        $this->menuModel = $this->model('Menu');
    }
    
    /**
     * Clients index
     */
    public function index() {
        // Get all clients
        $clients = $this->clientModel->getAllClients();
        
        $data = [
            'title' => 'Client Management',
            'clients' => $clients
        ];
        
        $this->view('clients/index', $data);
    }
    
    /**
     * Add client
     */
    public function add() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'rental_type' => trim($_POST['rental_type']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'rental_type_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate rental type
            if (empty($data['rental_type'])) {
                $data['rental_type_err'] = 'Please select rental type';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['rental_type_err']) && 
                empty($data['status_err'])) {
                
                // Create client
                $clientId = $this->clientModel->createClient($data);
                
                if ($clientId) {
                    // Client created
                    // Set flash message
                    // Redirect to clients index
                    $this->redirect('clients');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Add Client';
                
                $this->view('clients/add', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Add Client',
                'name' => '',
                'description' => '',
                'rental_type' => '',
                'status' => 'active',
                'name_err' => '',
                'rental_type_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('clients/add', $data);
        }
    }
    
    /**
     * Edit client
     * 
     * @param int $id Client ID
     */
    public function edit($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'rental_type' => trim($_POST['rental_type']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'rental_type_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate rental type
            if (empty($data['rental_type'])) {
                $data['rental_type_err'] = 'Please select rental type';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['rental_type_err']) && 
                empty($data['status_err'])) {
                
                // Update client
                if ($this->clientModel->updateClient($data)) {
                    // Client updated
                    // Set flash message
                    // Redirect to clients index
                    $this->redirect('clients');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Edit Client';
                
                $this->view('clients/edit', $data);
            }
        } else {
            // Get client
            $client = $this->clientModel->getClientById($id);
            
            // Check if client exists
            if (!$client) {
                $this->redirect('clients');
            }
            
            // Init data
            $data = [
                'title' => 'Edit Client',
                'id' => $client->id,
                'name' => $client->name,
                'description' => $client->description,
                'rental_type' => $client->rental_type,
                'status' => $client->status,
                'name_err' => '',
                'rental_type_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('clients/edit', $data);
        }
    }
    
    /**
     * Delete client
     * 
     * @param int $id Client ID
     */
    public function delete($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete client
            if ($this->clientModel->deleteClient($id)) {
                // Client deleted
                // Set flash message
                // Redirect to clients index
                $this->redirect('clients');
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('clients');
        }
    }
    
    /**
     * Manage client services
     * 
     * @param int $id Client ID
     */
    public function services($id) {
        // Get client
        $client = $this->clientModel->getClientById($id);
        
        // Check if client exists
        if (!$client) {
            $this->redirect('clients');
        }
        
        // Get all services
        $allServices = $this->serviceModel->getAllServices();
        
        // Get client services
        $clientServices = $this->serviceModel->getServicesByClientId($id);
        
        // Create array of client service IDs
        $clientServiceIds = [];
        foreach ($clientServices as $service) {
            $clientServiceIds[] = $service->id;
        }
        
        $data = [
            'title' => 'Manage Client Services',
            'client' => $client,
            'all_services' => $allServices,
            'client_service_ids' => $clientServiceIds
        ];
        
        $this->view('clients/services', $data);
    }
    
    /**
     * Assign service to client
     * 
     * @param int $clientId Client ID
     * @param int $serviceId Service ID
     */
    public function assignService($clientId, $serviceId) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Assign service to client
            if ($this->serviceModel->assignServiceToClient($clientId, $serviceId)) {
                // Service assigned
                // Set flash message
                // Redirect to client services
                $this->redirect('clients/services/' . $clientId);
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('clients');
        }
    }
    
    /**
     * Remove service from client
     * 
     * @param int $clientId Client ID
     * @param int $serviceId Service ID
     */
    public function removeService($clientId, $serviceId) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Remove service from client
            if ($this->serviceModel->removeServiceFromClient($clientId, $serviceId)) {
                // Service removed
                // Set flash message
                // Redirect to client services
                $this->redirect('clients/services/' . $clientId);
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('clients');
        }
    }
    
    /**
     * Manage client menus
     * 
     * @param int $id Client ID
     */
    public function menus($id) {
        // Get client
        $client = $this->clientModel->getClientById($id);
        
        // Check if client exists
        if (!$client) {
            $this->redirect('clients');
        }
        
        // Get all menus
        $allMenus = $this->menuModel->getAllMenus();
        
        // Get client menus
        $clientMenus = $this->menuModel->getMenusByClientId($id);
        
        // Create array of client menu IDs
        $clientMenuIds = [];
        foreach ($clientMenus as $menu) {
            $clientMenuIds[] = $menu->id;
        }
        
        $data = [
            'title' => 'Manage Client Menus',
            'client' => $client,
            'all_menus' => $allMenus,
            'client_menu_ids' => $clientMenuIds
        ];
        
        $this->view('clients/menus', $data);
    }
    
    /**
     * Assign menu to client
     * 
     * @param int $clientId Client ID
     * @param int $menuId Menu ID
     */
    public function assignMenu($clientId, $menuId) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Assign menu to client
            if ($this->menuModel->assignMenuToClient($clientId, $menuId)) {
                // Menu assigned
                // Set flash message
                // Redirect to client menus
                $this->redirect('clients/menus/' . $clientId);
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('clients');
        }
    }
    
    /**
     * Remove menu from client
     * 
     * @param int $clientId Client ID
     * @param int $menuId Menu ID
     */
    public function removeMenu($clientId, $menuId) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Remove menu from client
            if ($this->menuModel->removeMenuFromClient($clientId, $menuId)) {
                // Menu removed
                // Set flash message
                // Redirect to client menus
                $this->redirect('clients/menus/' . $clientId);
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('clients');
        }
    }
}

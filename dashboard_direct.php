<?php
/**
 * Standalone dashboard page that doesn't rely on the MVC framework
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    header('Location: ' . APP_URL . '/login.php');
    exit;
}

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['user_username'];
$full_name = $_SESSION['user_name'];
$role = $_SESSION['user_role'];
$client_id = $_SESSION['client_id'] ?? null;
$client_name = $_SESSION['client_name'] ?? null;
$rental_type = $_SESSION['rental_type'] ?? null;

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Get clients (for superadmin)
    $clients = [];
    if ($role === 'superadmin') {
        $stmt = $conn->query('SELECT * FROM clients ORDER BY id ASC');
        $clients = $stmt->fetchAll();
    }
    
    // Get services
    $services = [];
    if ($role === 'superadmin') {
        $stmt = $conn->query('SELECT * FROM services ORDER BY id ASC');
        $services = $stmt->fetchAll();
    } else if ($client_id) {
        $stmt = $conn->prepare('SELECT s.* FROM services s
                              INNER JOIN client_services cs ON s.id = cs.service_id
                              WHERE cs.client_id = :client_id AND cs.status = "active"
                              ORDER BY s.id ASC');
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();
        $services = $stmt->fetchAll();
    }
    
    // Get client info (for admin)
    $client = null;
    if ($role === 'admin' && $client_id) {
        $stmt = $conn->prepare('SELECT * FROM clients WHERE id = :id');
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();
        $client = $stmt->fetch();
    }
    
    // Get menus
    $menus = [];
    if ($role === 'superadmin') {
        $stmt = $conn->query('SELECT * FROM menus ORDER BY order_number ASC');
        $menus = $stmt->fetchAll();
    } else if ($client_id) {
        $stmt = $conn->prepare('SELECT m.* FROM menus m
                              INNER JOIN client_menus cm ON m.id = cm.menu_id
                              WHERE cm.client_id = :client_id AND cm.status = "active" AND m.status = "active"
                              ORDER BY m.order_number ASC');
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();
        $menus = $stmt->fetchAll();
    }
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            margin-bottom: 20px;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-dashboard:hover {
            transform: translateY(-5px);
            transition: transform 0.3s;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo APP_URL; ?>/dashboard_direct.php"><?php echo APP_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="<?php echo APP_URL; ?>/dashboard_direct.php">Dashboard</a>
                    </li>
                    
                    <?php if ($role === 'superadmin') : ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/clients.php">Clients</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/services.php">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/menus.php">Menus</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/users_list.php">User Management</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/add_admin.php">Add Admin</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php if ($role === 'admin') : ?>
                        <?php if ($rental_type === 'bicycle') : ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/bicycles.php">Bicycles</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if ($rental_type === 'motorcycle') : ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/motorcycles.php">Motorcycles</a>
                            </li>
                        <?php endif; ?>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/items.php"><?php echo ucfirst($rental_type); ?>s</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/rentals.php">Rentals</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/customers.php">Customers</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/reports.php">Reports</a>
                        </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo $full_name; ?>
                            <?php if ($client_name) : ?>
                                (<?php echo $client_name; ?>)
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Dashboard</h4>
                    </div>
                    <div class="card-body">
                        <h5>Welcome, <?php echo $full_name; ?>!</h5>
                        
                        <?php if ($role === 'superadmin') : ?>
                            <div class="row mt-4">
                                <div class="col-md-4">
                                    <div class="card bg-info text-white mb-4 card-dashboard">
                                        <div class="card-body">
                                            <h5 class="card-title">Clients</h5>
                                            <p class="card-text">Total: <?php echo count($clients); ?></p>
                                        </div>
                                        <div class="card-footer d-flex align-items-center justify-content-between">
                                            <a class="small text-white stretched-link" href="<?php echo APP_URL; ?>/clients.php">View Details</a>
                                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-success text-white mb-4 card-dashboard">
                                        <div class="card-body">
                                            <h5 class="card-title">Services</h5>
                                            <p class="card-text">Total: <?php echo count($services); ?></p>
                                        </div>
                                        <div class="card-footer d-flex align-items-center justify-content-between">
                                            <a class="small text-white stretched-link" href="<?php echo APP_URL; ?>/services.php">View Details</a>
                                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-warning text-white mb-4 card-dashboard">
                                        <div class="card-body">
                                            <h5 class="card-title">Menus</h5>
                                            <p class="card-text">Total: <?php echo count($menus); ?></p>
                                        </div>
                                        <div class="card-footer d-flex align-items-center justify-content-between">
                                            <a class="small text-white stretched-link" href="<?php echo APP_URL; ?>/menus.php">View Details</a>
                                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="card shadow">
                                        <div class="card-header">
                                            <h5 class="mb-0">Clients</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Name</th>
                                                            <th>Rental Type</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($clients as $client) : ?>
                                                            <tr>
                                                                <td><?php echo $client->id; ?></td>
                                                                <td><?php echo $client->name; ?></td>
                                                                <td>
                                                                    <?php if ($client->rental_type == 'bicycle') : ?>
                                                                        <span class="badge bg-primary">Bicycle</span>
                                                                    <?php else : ?>
                                                                        <span class="badge bg-danger">Motorcycle</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <?php if ($client->status == 'active') : ?>
                                                                        <span class="badge bg-success">Active</span>
                                                                    <?php else : ?>
                                                                        <span class="badge bg-danger">Inactive</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <a href="<?php echo APP_URL; ?>/client_services.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-info">Services</a>
                                                                    <a href="<?php echo APP_URL; ?>/client_menus.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-warning">Menus</a>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php else : ?>
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h5>Client Information</h5>
                                        <p><strong>Name:</strong> <?php echo $client->name; ?></p>
                                        <p><strong>Rental Type:</strong> 
                                            <?php if ($client->rental_type == 'bicycle') : ?>
                                                <span class="badge bg-primary">Bicycle</span>
                                            <?php else : ?>
                                                <span class="badge bg-danger">Motorcycle</span>
                                            <?php endif; ?>
                                        </p>
                                        <p><strong>Status:</strong> 
                                            <?php if ($client->status == 'active') : ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else : ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="card shadow">
                                        <div class="card-header">
                                            <h5 class="mb-0">Available Services</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Name</th>
                                                            <th>Description</th>
                                                            <th>Status</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($services as $service) : ?>
                                                            <tr>
                                                                <td><?php echo $service->id; ?></td>
                                                                <td><?php echo $service->name; ?></td>
                                                                <td><?php echo $service->description; ?></td>
                                                                <td>
                                                                    <?php if ($service->status == 'active') : ?>
                                                                        <span class="badge bg-success">Active</span>
                                                                    <?php else : ?>
                                                                        <span class="badge bg-danger">Inactive</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

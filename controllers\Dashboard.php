<?php
/**
 * Dashboard Controller
 */

class Dashboard extends Controller {
    private $menuModel;
    private $clientModel;
    private $serviceModel;

    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!$this->isLoggedIn()) {
            // Simply redirect to login without any checks
            $this->redirect('auth/login');
            return;
        }

        // Only load models if user is logged in
        try {
            $this->menuModel = $this->model('Menu');
            $this->clientModel = $this->model('Client');
            $this->serviceModel = $this->model('Service');
        } catch (Exception $e) {
            // If there's an error loading models, log it and redirect to login
            error_log('Error loading models: ' . $e->getMessage());
            $this->redirect('auth/login');
            return;
        }
    }

    /**
     * Login method - redirects to Auth controller
     */
    public function login() {
        $this->redirect('auth/login');
    }

    /**
     * Dashboard index
     */
    public function index() {
        // Get client ID (if admin)
        $clientId = $this->getClientId();

        // Get menus based on role and client
        if ($this->hasRole('superadmin')) {
            // Superadmin sees all menus
            $menus = $this->menuModel->getAllMenus();
            $clients = $this->clientModel->getAllClients();
            $services = $this->serviceModel->getAllServices();

            $data = [
                'title' => 'Dashboard',
                'menus' => $menus,
                'clients' => $clients,
                'services' => $services
            ];
        } else {
            // Admin sees only assigned menus
            $menus = $this->menuModel->getMenusByClientId($clientId);
            $client = $this->clientModel->getClientById($clientId);
            $services = $this->serviceModel->getServicesByClientId($clientId);

            $data = [
                'title' => 'Dashboard',
                'menus' => $menus,
                'client' => $client,
                'services' => $services
            ];
        }

        $this->view('dashboard/index', $data);
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($data['title']) ? $data['title'] . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo APP_URL; ?>/assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php if(isset($_SESSION['user_id'])) : ?>
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo APP_URL; ?>/dashboard"><?php echo APP_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/dashboard">Dashboard</a>
                        </li>
                        
                        <?php if(isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'superadmin') : ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/users">Users</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/clients">Clients</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/services">Services</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/menus">Menus</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if(isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                            <?php if(isset($_SESSION['rental_type']) && $_SESSION['rental_type'] == 'bicycle') : ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo APP_URL; ?>/bicycles">Bicycles</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php if(isset($_SESSION['rental_type']) && $_SESSION['rental_type'] == 'motorcycle') : ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo APP_URL; ?>/motorcycles">Motorcycles</a>
                                </li>
                            <?php endif; ?>
                            
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/rentals">Rentals</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/customers">Customers</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/reports">Reports</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <?php echo $_SESSION['user_name']; ?>
                                <?php if(isset($_SESSION['client_name'])) : ?>
                                    (<?php echo $_SESSION['client_name']; ?>)
                                <?php endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/users/profile">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/auth/logout">Logout</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php endif; ?>
    
    <div class="container mt-4">
        <?php if(isset($_SESSION['flash_message'])) : ?>
            <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['flash_message']); ?>
            <?php unset($_SESSION['flash_type']); ?>
        <?php endif; ?>

<?php
/**
 * Test login script to verify database connection and user credentials
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'saas_rental');
define('DB_USER', 'root');
define('DB_PASS', '');

// Start session
session_start();

// Function to test login
function testLogin($username, $password) {
    try {
        // Connect to database
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
        $options = [
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
        ];
        
        $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
        echo "Database connection successful!<br>";
        
        // Get user by username
        $stmt = $conn->prepare('SELECT * FROM users WHERE username = :username');
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $user = $stmt->fetch();
        
        if ($user) {
            echo "User found: " . $user->username . "<br>";
            
            // Verify password
            if (password_verify($password, $user->password)) {
                echo "Password verification successful!<br>";
                
                // Create session
                $_SESSION['user_id'] = $user->id;
                $_SESSION['user_username'] = $user->username;
                $_SESSION['user_email'] = $user->email;
                $_SESSION['user_name'] = $user->full_name;
                $_SESSION['user_role'] = $user->role;
                
                // If admin, set client ID
                if ($user->role == 'admin' && $user->client_id) {
                    $_SESSION['client_id'] = $user->client_id;
                    
                    // Get client info
                    $stmt = $conn->prepare('SELECT * FROM clients WHERE id = :id');
                    $stmt->bindParam(':id', $user->client_id);
                    $stmt->execute();
                    
                    $client = $stmt->fetch();
                    if ($client) {
                        $_SESSION['client_name'] = $client->name;
                        $_SESSION['rental_type'] = $client->rental_type;
                    }
                }
                
                echo "Session created successfully!<br>";
                echo "<pre>";
                print_r($_SESSION);
                echo "</pre>";
                
                echo "<a href='index.php'>Go to Dashboard</a>";
                return true;
            } else {
                echo "Password verification failed!<br>";
                return false;
            }
        } else {
            echo "User not found!<br>";
            return false;
        }
    } catch (PDOException $e) {
        echo "Database Error: " . $e->getMessage() . "<br>";
        return false;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (!empty($username) && !empty($password)) {
        testLogin($username, $password);
    } else {
        echo "Please enter username and password!<br>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Test Login</h4>
                    </div>
                    <div class="card-body">
                        <form action="test_login.php" method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Test Login</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="mt-3">
                    <p>Default credentials:</p>
                    <ul>
                        <li>Username: superadmin</li>
                        <li>Password: password</li>
                    </ul>
                </div>
                
                <div class="mt-3">
                    <a href="reset_password.php" class="btn btn-warning">Reset Superadmin Password</a>
                    <a href="debug.php" class="btn btn-info">Debug Information</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

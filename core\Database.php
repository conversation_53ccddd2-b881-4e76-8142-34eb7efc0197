<?php
/**
 * Database class for handling database connections and queries
 */

class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;

    private $conn;
    private $stmt;
    private $error;

    /**
     * Constructor - Creates a database connection
     */
    public function __construct() {
        // Set DSN
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname;

        // Set options
        $options = [
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
        ];

        // Create PDO instance
        try {
            $this->conn = new PDO($dsn, $this->user, $this->pass, $options);
        } catch (PDOException $e) {
            $this->error = $e->getMessage();
            // Don't echo error, throw it so it can be caught
            throw new Exception('Database Connection Error: ' . $this->error);
        }
    }

    /**
     * Prepare statement with query
     *
     * @param string $sql SQL query
     * @return void
     */
    public function query($sql) {
        $this->stmt = $this->conn->prepare($sql);
    }

    /**
     * Bind values to prepared statement
     *
     * @param string $param Parameter name
     * @param mixed $value Parameter value
     * @param mixed $type Parameter type
     * @return void
     */
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }

        $this->stmt->bindValue($param, $value, $type);
    }

    /**
     * Execute the prepared statement
     *
     * @return bool True on success, false on failure
     */
    public function execute() {
        return $this->stmt->execute();
    }

    /**
     * Get result set as array of objects
     *
     * @return array Result set
     */
    public function resultSet() {
        $this->execute();
        return $this->stmt->fetchAll();
    }

    /**
     * Get single record as object
     *
     * @return object Single record
     */
    public function single() {
        $this->execute();
        return $this->stmt->fetch();
    }

    /**
     * Get row count
     *
     * @return int Row count
     */
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    /**
     * Get last inserted ID
     *
     * @return int Last inserted ID
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }
}

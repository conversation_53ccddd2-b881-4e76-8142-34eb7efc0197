<?php
/**
 * Admin Dashboard - Specialized dashboard for admin users
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Get user information
$user_id = $_SESSION['user_id'];
$client_id = $_SESSION['client_id'];
$client_name = $_SESSION['client_name'];
$rental_type = $_SESSION['rental_type'];
$user_name = $_SESSION['user_name'];

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Get statistics for this client
    $stats = [];
    
    // Total items
    $stmt = $conn->prepare('SELECT COUNT(*) as total FROM items WHERE client_id = :client_id');
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $stats['total_items'] = $stmt->fetchColumn();
    
    // Available items
    $stmt = $conn->prepare('SELECT COUNT(*) as available FROM items WHERE client_id = :client_id AND status = "available"');
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $stats['available_items'] = $stmt->fetchColumn();
    
    // Rented items
    $stmt = $conn->prepare('SELECT COUNT(*) as rented FROM items WHERE client_id = :client_id AND status = "rented"');
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $stats['rented_items'] = $stmt->fetchColumn();
    
    // Maintenance items
    $stmt = $conn->prepare('SELECT COUNT(*) as maintenance FROM items WHERE client_id = :client_id AND status = "maintenance"');
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $stats['maintenance_items'] = $stmt->fetchColumn();
    
    // Recent items (last 5)
    $stmt = $conn->prepare('SELECT * FROM items WHERE client_id = :client_id ORDER BY created_at DESC LIMIT 5');
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $recent_items = $stmt->fetchAll();
    
    // Low stock alert (items that need attention)
    $stmt = $conn->prepare('SELECT * FROM items WHERE client_id = :client_id AND status IN ("maintenance", "inactive") ORDER BY updated_at DESC LIMIT 5');
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $alert_items = $stmt->fetchAll();
    
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo $client_name; ?> - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .stat-card {
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="admin_dashboard.php">
                <i class="fas fa-<?php echo $rental_type === 'bicycle' ? 'bicycle' : 'motorcycle'; ?>"></i>
                <?php echo $client_name; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="items.php">
                            <i class="fas fa-<?php echo $rental_type === 'bicycle' ? 'bicycle' : 'motorcycle'; ?>"></i>
                            My <?php echo ucfirst($rental_type); ?>s
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_item.php">
                            <i class="fas fa-plus"></i>
                            Add <?php echo ucfirst($rental_type); ?>
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $user_name; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h2 class="card-title">Welcome, <?php echo $user_name; ?>!</h2>
                        <p class="card-text">Manage your <?php echo $rental_type; ?> rental inventory for <?php echo $client_name; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-info text-white stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $stats['total_items']; ?></h4>
                                <p class="mb-0">Total Items</p>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <a href="items.php" class="text-white text-decoration-none">
                            View All <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $stats['available_items']; ?></h4>
                                <p class="mb-0">Available</p>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <span class="text-white">Ready to rent</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $stats['rented_items']; ?></h4>
                                <p class="mb-0">Rented</p>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <span class="text-white">Currently out</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $stats['maintenance_items']; ?></h4>
                                <p class="mb-0">Maintenance</p>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <span class="text-white">Needs attention</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <a href="add_item.php" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-plus"></i><br>
                                    Add New <?php echo ucfirst($rental_type); ?>
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="items.php" class="btn btn-info btn-lg w-100">
                                    <i class="fas fa-list"></i><br>
                                    View All Items
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="items.php?status=maintenance" class="btn btn-warning btn-lg w-100">
                                    <i class="fas fa-tools"></i><br>
                                    Maintenance Items
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

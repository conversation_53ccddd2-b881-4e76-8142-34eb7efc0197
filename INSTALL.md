# Installation Guide for SAAS Service Rental

Follow these steps to install and set up the SAAS Service Rental application.

## Prerequisites

- XAMPP (or similar package with Apache, MySQL, and PHP)
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache with mod_rewrite enabled

## Installation Steps

1. **Clone or Download the Repository**

   Place the project files in your XAMPP htdocs directory:
   ```
   C:\xampp\htdocs\ProjectUAS\
   ```

2. **Create the Database**

   There are two ways to create the database:

   **Option 1: Using the initialization script**
   - Open your web browser and navigate to:
     ```
     http://localhost/ProjectUAS/init_db.php
     ```
   - This script will create the database and tables automatically.

   **Option 2: Manual setup**
   - Create a new database named `saas_rental` in phpMyAdmin
   - Import the `database.sql` file into the newly created database

3. **Configure the Application**

   If needed, update the configuration in `config/config.php`:
   - Database credentials
   - Application URL
   - Other settings

4. **Set Permissions**

   Make sure the web server has write permissions to the following directories:
   - `uploads/` (if you plan to upload files)
   - Any other directories that need write access

5. **Enable mod_rewrite**

   Make sure Apache's mod_rewrite is enabled:
   - Open XAMPP Control Panel
   - Click on "Config" for Apache
   - Select "httpd.conf"
   - Find and uncomment the line: `LoadModule rewrite_module modules/mod_rewrite.so`
   - Restart Apache

6. **Access the Application**

   Open your web browser and navigate to:
   ```
   http://localhost/ProjectUAS/
   ```

7. **Login with Default Credentials**

   - **Superadmin**:
     - Username: superadmin
     - Password: password

## Post-Installation

1. **Change Default Password**

   For security reasons, immediately change the default superadmin password after logging in.

2. **Create Clients**

   Create the two clients:
   - Client A: Bicycle Rental
   - Client B: Motorcycle Rental

3. **Create Admin Users**

   Create admin users for each client.

4. **Configure Services**

   Assign appropriate services to each client.

5. **Configure Menus**

   Assign appropriate menus to each client.

## Troubleshooting

- **404 Not Found**: Make sure mod_rewrite is enabled and .htaccess is properly configured.
- **Database Connection Error**: Check your database credentials in `config/config.php`.
- **Blank Page**: Check PHP error logs for details.

## Support

If you encounter any issues, please contact the developer for assistance.

<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /ProjectUAS/
    
    # Prevent redirect loops
    RewriteCond %{ENV:REDIRECT_STATUS} 200
    RewriteRule .* - [L]
    
    # Redirect to index.php if not a file or directory
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
</IfModule>

# PHP settings
php_flag display_errors on
php_value error_reporting E_ALL

# Default index
DirectoryIndex index.php

# Prevent infinite loops
<IfModule mod_headers.c>
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</IfModule>
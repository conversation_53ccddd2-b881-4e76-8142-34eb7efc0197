<?php
/**
 * Edit Item Page (for Admin users)
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Get user information
$user_id = $_SESSION['user_id'];
$client_id = $_SESSION['client_id'];
$client_name = $_SESSION['client_name'];
$rental_type = $_SESSION['rental_type'];

// Get item ID from URL
$item_id = $_GET['id'] ?? 0;

// Initialize variables
$name = '';
$description = '';
$brand = '';
$model = '';
$year = '';
$color = '';
$price_per_day = '';
$status = 'available';
$image_url = '';

$name_err = '';
$brand_err = '';
$year_err = '';
$color_err = '';
$price_err = '';
$success_msg = '';

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Get item data
    $stmt = $conn->prepare('SELECT * FROM items WHERE id = :id AND client_id = :client_id');
    $stmt->bindParam(':id', $item_id);
    $stmt->bindParam(':client_id', $client_id);
    $stmt->execute();
    $item = $stmt->fetch();
    
    if (!$item) {
        header('Location: items.php');
        exit;
    }
    
    // Set initial values
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $name = $item->name;
        $description = $item->description;
        $brand = $item->brand;
        $model = $item->model;
        $year = $item->year;
        $color = $item->color;
        $price_per_day = $item->price_per_day;
        $status = $item->status;
        $image_url = $item->image_url;
    }
    
    // Process form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get form data
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $brand = trim($_POST['brand'] ?? '');
        $model = trim($_POST['model'] ?? '');
        $year = trim($_POST['year'] ?? '');
        $color = trim($_POST['color'] ?? '');
        $price_per_day = trim($_POST['price_per_day'] ?? '');
        $status = trim($_POST['status'] ?? 'available');
        $image_url = trim($_POST['image_url'] ?? '');
        
        // Validate name
        if (empty($name)) {
            $name_err = 'Please enter item name';
        }
        
        // Validate brand
        if (empty($brand)) {
            $brand_err = 'Please enter brand';
        }
        
        // Validate year
        if (empty($year)) {
            $year_err = 'Please enter year';
        } elseif (!is_numeric($year) || $year < 1900 || $year > date('Y') + 1) {
            $year_err = 'Please enter a valid year';
        }
        
        // Validate color
        if (empty($color)) {
            $color_err = 'Please enter color';
        }
        
        // Validate price
        if (empty($price_per_day)) {
            $price_err = 'Please enter price per day';
        } elseif (!is_numeric($price_per_day) || $price_per_day <= 0) {
            $price_err = 'Please enter a valid price';
        }
        
        // If no errors, update the item
        if (empty($name_err) && empty($brand_err) && empty($year_err) && empty($color_err) && empty($price_err)) {
            $stmt = $conn->prepare('UPDATE items SET name = :name, description = :description, brand = :brand, model = :model, year = :year, color = :color, price_per_day = :price_per_day, status = :status, image_url = :image_url WHERE id = :id AND client_id = :client_id');
            
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':brand', $brand);
            $stmt->bindParam(':model', $model);
            $stmt->bindParam(':year', $year);
            $stmt->bindParam(':color', $color);
            $stmt->bindParam(':price_per_day', $price_per_day);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':image_url', $image_url);
            $stmt->bindParam(':id', $item_id);
            $stmt->bindParam(':client_id', $client_id);
            
            if ($stmt->execute()) {
                $success_msg = ucfirst($rental_type) . ' updated successfully!';
            }
        }
    }
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit <?php echo ucfirst($rental_type); ?> - <?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard_direct.php"><?php echo APP_NAME; ?></a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard_direct.php">Dashboard</a>
                <a class="nav-link" href="items.php"><?php echo ucfirst($rental_type); ?>s</a>
                <a class="nav-link" href="logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Edit <?php echo ucfirst($rental_type); ?> - <?php echo $client_name; ?></h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($success_msg)) : ?>
                            <div class="alert alert-success"><?php echo $success_msg; ?></div>
                        <?php endif; ?>

                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?id=' . $item_id; ?>" method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label"><?php echo ucfirst($rental_type); ?> Name *</label>
                                        <input type="text" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>">
                                        <div class="invalid-feedback"><?php echo $name_err; ?></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="brand" class="form-label">Brand *</label>
                                        <input type="text" class="form-control <?php echo (!empty($brand_err)) ? 'is-invalid' : ''; ?>" id="brand" name="brand" value="<?php echo htmlspecialchars($brand); ?>">
                                        <div class="invalid-feedback"><?php echo $brand_err; ?></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($description); ?></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="model" class="form-label">Model</label>
                                        <input type="text" class="form-control" id="model" name="model" value="<?php echo htmlspecialchars($model); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="year" class="form-label">Year *</label>
                                        <input type="number" class="form-control <?php echo (!empty($year_err)) ? 'is-invalid' : ''; ?>" id="year" name="year" value="<?php echo $year; ?>" min="1900" max="<?php echo date('Y') + 1; ?>">
                                        <div class="invalid-feedback"><?php echo $year_err; ?></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="color" class="form-label">Color *</label>
                                        <input type="text" class="form-control <?php echo (!empty($color_err)) ? 'is-invalid' : ''; ?>" id="color" name="color" value="<?php echo htmlspecialchars($color); ?>">
                                        <div class="invalid-feedback"><?php echo $color_err; ?></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="price_per_day" class="form-label">Price per Day (Rp) *</label>
                                        <input type="number" class="form-control <?php echo (!empty($price_err)) ? 'is-invalid' : ''; ?>" id="price_per_day" name="price_per_day" value="<?php echo $price_per_day; ?>" min="0" step="1000">
                                        <div class="invalid-feedback"><?php echo $price_err; ?></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="available" <?php echo ($status == 'available') ? 'selected' : ''; ?>>Available</option>
                                            <option value="rented" <?php echo ($status == 'rented') ? 'selected' : ''; ?>>Rented</option>
                                            <option value="maintenance" <?php echo ($status == 'maintenance') ? 'selected' : ''; ?>>Maintenance</option>
                                            <option value="inactive" <?php echo ($status == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="image_url" class="form-label">Image URL</label>
                                        <input type="url" class="form-control" id="image_url" name="image_url" value="<?php echo htmlspecialchars($image_url); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="items.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Update <?php echo ucfirst($rental_type); ?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

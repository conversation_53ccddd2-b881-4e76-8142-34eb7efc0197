<?php
/**
 * Menu Model
 */

class Menu extends Model {
    /**
     * Get menu by ID
     * 
     * @param int $id Menu ID
     * @return object|bool Menu object or false if not found
     */
    public function getMenuById($id) {
        $this->db->query('SELECT * FROM menus WHERE id = :id');
        $this->db->bind(':id', $id);
        
        $row = $this->db->single();
        
        return $row ? $row : false;
    }
    
    /**
     * Get all menus
     * 
     * @return array Array of menu objects
     */
    public function getAllMenus() {
        $this->db->query('SELECT * FROM menus ORDER BY order_number ASC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get menus by client ID
     * 
     * @param int $clientId Client ID
     * @return array Array of menu objects
     */
    public function getMenusByClientId($clientId) {
        $this->db->query('SELECT m.* FROM menus m
                          INNER JOIN client_menus cm ON m.id = cm.menu_id
                          WHERE cm.client_id = :client_id AND cm.status = "active" AND m.status = "active"
                          ORDER BY m.order_number ASC');
        $this->db->bind(':client_id', $clientId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get submenus by menu ID
     * 
     * @param int $menuId Menu ID
     * @return array Array of submenu objects
     */
    public function getSubmenusByMenuId($menuId) {
        $this->db->query('SELECT * FROM submenus WHERE menu_id = :menu_id AND status = "active" ORDER BY order_number ASC');
        $this->db->bind(':menu_id', $menuId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get submenus by client ID and menu ID
     * 
     * @param int $clientId Client ID
     * @param int $menuId Menu ID
     * @return array Array of submenu objects
     */
    public function getSubmenusByClientAndMenuId($clientId, $menuId) {
        $this->db->query('SELECT s.* FROM submenus s
                          INNER JOIN client_submenus cs ON s.id = cs.submenu_id
                          WHERE cs.client_id = :client_id AND s.menu_id = :menu_id 
                          AND cs.status = "active" AND s.status = "active"
                          ORDER BY s.order_number ASC');
        $this->db->bind(':client_id', $clientId);
        $this->db->bind(':menu_id', $menuId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Create new menu
     * 
     * @param array $data Menu data
     * @return bool True on success, false on failure
     */
    public function createMenu($data) {
        $this->db->query('INSERT INTO menus (name, icon, url, order_number, status) 
                          VALUES (:name, :icon, :url, :order_number, :status)');
        
        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':icon', $data['icon']);
        $this->db->bind(':url', $data['url']);
        $this->db->bind(':order_number', $data['order_number']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update menu
     * 
     * @param array $data Menu data
     * @return bool True on success, false on failure
     */
    public function updateMenu($data) {
        $this->db->query('UPDATE menus SET name = :name, icon = :icon, url = :url, 
                          order_number = :order_number, status = :status 
                          WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':icon', $data['icon']);
        $this->db->bind(':url', $data['url']);
        $this->db->bind(':order_number', $data['order_number']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Delete menu
     * 
     * @param int $id Menu ID
     * @return bool True on success, false on failure
     */
    public function deleteMenu($id) {
        $this->db->query('DELETE FROM menus WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $id);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Create new submenu
     * 
     * @param array $data Submenu data
     * @return bool True on success, false on failure
     */
    public function createSubmenu($data) {
        $this->db->query('INSERT INTO submenus (menu_id, name, icon, url, order_number, status) 
                          VALUES (:menu_id, :name, :icon, :url, :order_number, :status)');
        
        // Bind values
        $this->db->bind(':menu_id', $data['menu_id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':icon', $data['icon']);
        $this->db->bind(':url', $data['url']);
        $this->db->bind(':order_number', $data['order_number']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update submenu
     * 
     * @param array $data Submenu data
     * @return bool True on success, false on failure
     */
    public function updateSubmenu($data) {
        $this->db->query('UPDATE submenus SET menu_id = :menu_id, name = :name, icon = :icon, 
                          url = :url, order_number = :order_number, status = :status 
                          WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':menu_id', $data['menu_id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':icon', $data['icon']);
        $this->db->bind(':url', $data['url']);
        $this->db->bind(':order_number', $data['order_number']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Delete submenu
     * 
     * @param int $id Submenu ID
     * @return bool True on success, false on failure
     */
    public function deleteSubmenu($id) {
        $this->db->query('DELETE FROM submenus WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $id);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Assign menu to client
     * 
     * @param int $clientId Client ID
     * @param int $menuId Menu ID
     * @return bool True on success, false on failure
     */
    public function assignMenuToClient($clientId, $menuId) {
        $this->db->query('INSERT INTO client_menus (client_id, menu_id) 
                          VALUES (:client_id, :menu_id)');
        
        // Bind values
        $this->db->bind(':client_id', $clientId);
        $this->db->bind(':menu_id', $menuId);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Remove menu from client
     * 
     * @param int $clientId Client ID
     * @param int $menuId Menu ID
     * @return bool True on success, false on failure
     */
    public function removeMenuFromClient($clientId, $menuId) {
        $this->db->query('DELETE FROM client_menus 
                          WHERE client_id = :client_id AND menu_id = :menu_id');
        
        // Bind values
        $this->db->bind(':client_id', $clientId);
        $this->db->bind(':menu_id', $menuId);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Assign submenu to client
     * 
     * @param int $clientId Client ID
     * @param int $submenuId Submenu ID
     * @return bool True on success, false on failure
     */
    public function assignSubmenuToClient($clientId, $submenuId) {
        $this->db->query('INSERT INTO client_submenus (client_id, submenu_id) 
                          VALUES (:client_id, :submenu_id)');
        
        // Bind values
        $this->db->bind(':client_id', $clientId);
        $this->db->bind(':submenu_id', $submenuId);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Remove submenu from client
     * 
     * @param int $clientId Client ID
     * @param int $submenuId Submenu ID
     * @return bool True on success, false on failure
     */
    public function removeSubmenuFromClient($clientId, $submenuId) {
        $this->db->query('DELETE FROM client_submenus 
                          WHERE client_id = :client_id AND submenu_id = :submenu_id');
        
        // Bind values
        $this->db->bind(':client_id', $clientId);
        $this->db->bind(':submenu_id', $submenuId);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
}

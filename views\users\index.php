<?php require_once BASE_PATH . '/views/includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">User Management</h4>
                <a href="<?php echo APP_URL; ?>/users/add" class="btn btn-light">Add User</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Full Name</th>
                                <th>Role</th>
                                <th>Client</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($data['users'] as $user) : ?>
                                <tr>
                                    <td><?php echo $user->id; ?></td>
                                    <td><?php echo $user->username; ?></td>
                                    <td><?php echo $user->email; ?></td>
                                    <td><?php echo $user->full_name; ?></td>
                                    <td>
                                        <?php if($user->role == 'superadmin') : ?>
                                            <span class="badge bg-danger">Superadmin</span>
                                        <?php else : ?>
                                            <span class="badge bg-primary">Admin</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($user->client_id) : ?>
                                            <?php foreach($data['clients'] as $client) : ?>
                                                <?php if($client->id == $user->client_id) : ?>
                                                    <?php echo $client->name; ?>
                                                    <?php break; ?>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        <?php else : ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo APP_URL; ?>/users/edit/<?php echo $user->id; ?>" class="btn btn-sm btn-primary">Edit</a>
                                        <form class="d-inline" action="<?php echo APP_URL; ?>/users/delete/<?php echo $user->id; ?>" method="post">
                                            <button type="submit" class="btn btn-sm btn-danger btn-delete">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once BASE_PATH . '/views/includes/footer.php'; ?>

<?php
/**
 * Clear session and cookies to reset application state
 */

// Start session
session_start();

// Clear session variables
session_unset();

// Destroy session
session_destroy();

// Clear cookies
if (isset($_SERVER['HTTP_COOKIE'])) {
    $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
    foreach($cookies as $cookie) {
        $parts = explode('=', $cookie);
        $name = trim($parts[0]);
        setcookie($name, '', time()-1000);
        setcookie($name, '', time()-1000, '/');
    }
}

// Redirect to index
header('Location: index.php');
exit;
?>

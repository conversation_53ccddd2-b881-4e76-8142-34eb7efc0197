<?php
/**
 * Test Dashboard UI - Preview the new dashboard layout
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Simulate superadmin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['user_username'] = 'superadmin';
$_SESSION['user_name'] = 'Super Admin';
$_SESSION['user_role'] = 'superadmin';

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['user_username'];
$full_name = $_SESSION['user_name'];
$role = $_SESSION['user_role'];

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Get clients and admin users (for superadmin)
    $clients = [];
    $admin_users = [];
    if ($role === 'superadmin') {
        $stmt = $conn->query('SELECT * FROM clients ORDER BY id ASC');
        $clients = $stmt->fetchAll();
        
        // Get admin users count
        $stmt = $conn->query('SELECT COUNT(*) as count FROM users WHERE role = "admin"');
        $admin_count = $stmt->fetchColumn();
    }
    
    // Get services
    $services = [];
    if ($role === 'superadmin') {
        $stmt = $conn->query('SELECT * FROM services ORDER BY id ASC');
        $services = $stmt->fetchAll();
    }
    
    // Get menus
    $menus = [];
    if ($role === 'superadmin') {
        $stmt = $conn->query('SELECT * FROM menus ORDER BY order_number ASC');
        $menus = $stmt->fetchAll();
    }
    
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Preview - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-dashboard:hover {
            transform: translateY(-5px);
            transition: transform 0.3s;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#"><?php echo APP_NAME; ?></a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">Dashboard Preview</span>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Dashboard Preview</h4>
                    </div>
                    <div class="card-body">
                        <h5>Welcome, <?php echo $full_name; ?>!</h5>
                        
                        <!-- Statistics Cards -->
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card bg-info text-white mb-4 card-dashboard">
                                    <div class="card-body">
                                        <h5 class="card-title">Admin Users</h5>
                                        <p class="card-text">Total: <?php echo $admin_count ?? 0; ?></p>
                                    </div>
                                    <div class="card-footer d-flex align-items-center justify-content-between">
                                        <a class="small text-white stretched-link" href="users_list.php">View Details</a>
                                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white mb-4 card-dashboard">
                                    <div class="card-body">
                                        <h5 class="card-title">Services</h5>
                                        <p class="card-text">Total: <?php echo count($services); ?></p>
                                    </div>
                                    <div class="card-footer d-flex align-items-center justify-content-between">
                                        <a class="small text-white stretched-link" href="services.php">View Details</a>
                                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-white mb-4 card-dashboard">
                                    <div class="card-body">
                                        <h5 class="card-title">Business Types</h5>
                                        <p class="card-text">Total: <?php echo count($clients); ?></p>
                                    </div>
                                    <div class="card-footer d-flex align-items-center justify-content-between">
                                        <a class="small text-white stretched-link" href="clients.php">View Details</a>
                                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Main Table: Clients (Admin Users) -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card shadow">
                                    <div class="card-header">
                                        <h5 class="mb-0">Clients (Admin Users)</h5>
                                    </div>
                                    <div class="card-body">
                                        <?php
                                        // Get all admin users with client information
                                        $stmt = $conn->prepare('SELECT u.*, c.name as client_name, c.rental_type 
                                                              FROM users u 
                                                              LEFT JOIN clients c ON u.client_id = c.id 
                                                              WHERE u.role = "admin" 
                                                              ORDER BY u.created_at DESC');
                                        $stmt->execute();
                                        $admin_users = $stmt->fetchAll();
                                        ?>
                                        
                                        <?php if (!empty($admin_users)) : ?>
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Name</th>
                                                            <th>Rental Type</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($admin_users as $admin) : ?>
                                                            <tr>
                                                                <td><?php echo $admin->id; ?></td>
                                                                <td>
                                                                    <strong><?php echo htmlspecialchars($admin->full_name); ?></strong>
                                                                    <br><small class="text-muted">@<?php echo htmlspecialchars($admin->username); ?></small>
                                                                    <br><small class="text-muted"><?php echo htmlspecialchars($admin->email); ?></small>
                                                                </td>
                                                                <td>
                                                                    <?php if ($admin->rental_type == 'bicycle') : ?>
                                                                        <span class="badge bg-primary">Bicycle</span>
                                                                    <?php elseif ($admin->rental_type == 'motorcycle') : ?>
                                                                        <span class="badge bg-danger">Motorcycle</span>
                                                                    <?php else : ?>
                                                                        <span class="badge bg-secondary">No Client</span>
                                                                    <?php endif; ?>
                                                                    <?php if ($admin->client_name) : ?>
                                                                        <br><small class="text-muted"><?php echo htmlspecialchars($admin->client_name); ?></small>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-success">Active</span>
                                                                </td>
                                                                <td>
                                                                    <a href="edit_user.php?id=<?php echo $admin->id; ?>" class="btn btn-sm btn-info">Edit</a>
                                                                    <a href="delete_user.php?id=<?php echo $admin->id; ?>" class="btn btn-sm btn-warning" onclick="return confirm('Are you sure?')">Delete</a>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php else : ?>
                                            <div class="text-center py-4">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">No Admin Users Found</h5>
                                                <p class="text-muted">Start by adding your first admin user.</p>
                                                <a href="add_admin.php" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> Add Admin User
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="text-center mt-3">
                                            <a href="add_admin.php" class="btn btn-success">
                                                <i class="fas fa-plus"></i> Add New Admin
                                            </a>
                                            <a href="users_list.php" class="btn btn-info">
                                                <i class="fas fa-list"></i> Manage All Users
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Dashboard Updated!</h5>
                                <p>Tabel "Clients" sekarang menampilkan <strong>Admin Users</strong> yang sudah ditambahkan, bukan client companies.</p>
                                <ul>
                                    <li><strong>Admin Users:</strong> Menampilkan jumlah admin yang sudah dibuat</li>
                                    <li><strong>Tabel Clients:</strong> Menampilkan daftar admin users dengan informasi lengkap</li>
                                    <li><strong>Actions:</strong> Edit dan Delete admin users</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-block">
                                <a href="dashboard_direct.php" class="btn btn-primary">Go to Real Dashboard</a>
                                <a href="add_admin.php" class="btn btn-success">Add Admin User</a>
                                <a href="users_list.php" class="btn btn-info">View All Users</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

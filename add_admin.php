<?php
/**
 * Add Admin User Page
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Check if user is logged in and is superadmin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'superadmin') {
    header('Location: login.php');
    exit;
}

// Initialize variables
$username = '';
$email = '';
$full_name = '';
$client_id = '';
$username_err = '';
$email_err = '';
$full_name_err = '';
$password_err = '';
$client_id_err = '';
$success_msg = '';

// Connect to database
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Get clients for dropdown
    $stmt = $conn->query('SELECT * FROM clients ORDER BY name ASC');
    $clients = $stmt->fetchAll();
    
    // Process form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get form data
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $full_name = trim($_POST['full_name'] ?? '');
        $password = trim($_POST['password'] ?? '');
        $confirm_password = trim($_POST['confirm_password'] ?? '');
        $client_id = trim($_POST['client_id'] ?? '');
        
        // Validate username
        if (empty($username)) {
            $username_err = 'Please enter username';
        } else {
            // Check if username already exists
            $stmt = $conn->prepare('SELECT id FROM users WHERE username = :username');
            $stmt->bindParam(':username', $username);
            $stmt->execute();
            if ($stmt->fetch()) {
                $username_err = 'Username already exists';
            }
        }
        
        // Validate email
        if (empty($email)) {
            $email_err = 'Please enter email';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $email_err = 'Please enter a valid email';
        } else {
            // Check if email already exists
            $stmt = $conn->prepare('SELECT id FROM users WHERE email = :email');
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            if ($stmt->fetch()) {
                $email_err = 'Email already exists';
            }
        }
        
        // Validate full name
        if (empty($full_name)) {
            $full_name_err = 'Please enter full name';
        }
        
        // Validate password
        if (empty($password)) {
            $password_err = 'Please enter password';
        } elseif (strlen($password) < 6) {
            $password_err = 'Password must be at least 6 characters';
        } elseif ($password !== $confirm_password) {
            $password_err = 'Passwords do not match';
        }
        
        // Validate client
        if (empty($client_id)) {
            $client_id_err = 'Please select a client';
        }
        
        // If no errors, create the admin user
        if (empty($username_err) && empty($email_err) && empty($full_name_err) && empty($password_err) && empty($client_id_err)) {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare('INSERT INTO users (username, password, email, full_name, role, client_id) VALUES (:username, :password, :email, :full_name, :role, :client_id)');
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindValue(':role', 'admin');
            $stmt->bindParam(':client_id', $client_id);
            
            if ($stmt->execute()) {
                $success_msg = 'Admin user created successfully!';
                // Clear form data
                $username = '';
                $email = '';
                $full_name = '';
                $client_id = '';
            }
        }
    }
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Admin User - <?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard_direct.php"><?php echo APP_NAME; ?></a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard_direct.php">Dashboard</a>
                <a class="nav-link" href="logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Add Admin User</h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($success_msg)) : ?>
                            <div class="alert alert-success"><?php echo $success_msg; ?></div>
                        <?php endif; ?>

                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" id="username" name="username" value="<?php echo $username; ?>">
                                        <div class="invalid-feedback"><?php echo $username_err; ?></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" id="email" name="email" value="<?php echo $email; ?>">
                                        <div class="invalid-feedback"><?php echo $email_err; ?></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="full_name" class="form-label">Full Name</label>
                                <input type="text" class="form-control <?php echo (!empty($full_name_err)) ? 'is-invalid' : ''; ?>" id="full_name" name="full_name" value="<?php echo $full_name; ?>">
                                <div class="invalid-feedback"><?php echo $full_name_err; ?></div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password</label>
                                        <input type="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" id="password" name="password">
                                        <div class="invalid-feedback"><?php echo $password_err; ?></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Confirm Password</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="client_id" class="form-label">Client</label>
                                <select class="form-select <?php echo (!empty($client_id_err)) ? 'is-invalid' : ''; ?>" id="client_id" name="client_id">
                                    <option value="">Select Client</option>
                                    <?php foreach ($clients as $client) : ?>
                                        <option value="<?php echo $client->id; ?>" <?php echo ($client_id == $client->id) ? 'selected' : ''; ?>>
                                            <?php echo $client->name; ?> (<?php echo ucfirst($client->rental_type); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback"><?php echo $client_id_err; ?></div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="dashboard_direct.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Create Admin User</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

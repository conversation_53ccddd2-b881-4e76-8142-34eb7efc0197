<?php
/**
 * Service Model
 */

class Service extends Model {
    /**
     * Get service by ID
     * 
     * @param int $id Service ID
     * @return object|bool Service object or false if not found
     */
    public function getServiceById($id) {
        $this->db->query('SELECT * FROM services WHERE id = :id');
        $this->db->bind(':id', $id);
        
        $row = $this->db->single();
        
        return $row ? $row : false;
    }
    
    /**
     * Get all services
     * 
     * @return array Array of service objects
     */
    public function getAllServices() {
        $this->db->query('SELECT * FROM services ORDER BY id ASC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get services by client ID
     * 
     * @param int $clientId Client ID
     * @return array Array of service objects
     */
    public function getServicesByClientId($clientId) {
        $this->db->query('SELECT s.* FROM services s
                          INNER JOIN client_services cs ON s.id = cs.service_id
                          WHERE cs.client_id = :client_id AND cs.status = "active"
                          ORDER BY s.id ASC');
        $this->db->bind(':client_id', $clientId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Create new service
     * 
     * @param array $data Service data
     * @return bool True on success, false on failure
     */
    public function createService($data) {
        $this->db->query('INSERT INTO services (name, description, status) 
                          VALUES (:name, :description, :status)');
        
        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update service
     * 
     * @param array $data Service data
     * @return bool True on success, false on failure
     */
    public function updateService($data) {
        $this->db->query('UPDATE services SET name = :name, description = :description, 
                          status = :status WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':status', $data['status']);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Delete service
     * 
     * @param int $id Service ID
     * @return bool True on success, false on failure
     */
    public function deleteService($id) {
        $this->db->query('DELETE FROM services WHERE id = :id');
        
        // Bind values
        $this->db->bind(':id', $id);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Assign service to client
     * 
     * @param int $clientId Client ID
     * @param int $serviceId Service ID
     * @return bool True on success, false on failure
     */
    public function assignServiceToClient($clientId, $serviceId) {
        $this->db->query('INSERT INTO client_services (client_id, service_id) 
                          VALUES (:client_id, :service_id)');
        
        // Bind values
        $this->db->bind(':client_id', $clientId);
        $this->db->bind(':service_id', $serviceId);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Remove service from client
     * 
     * @param int $clientId Client ID
     * @param int $serviceId Service ID
     * @return bool True on success, false on failure
     */
    public function removeServiceFromClient($clientId, $serviceId) {
        $this->db->query('DELETE FROM client_services 
                          WHERE client_id = :client_id AND service_id = :service_id');
        
        // Bind values
        $this->db->bind(':client_id', $clientId);
        $this->db->bind(':service_id', $serviceId);
        
        // Execute
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
}

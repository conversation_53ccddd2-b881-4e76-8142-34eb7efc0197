<?php
/**
 * Debug script to help diagnose redirect loops and other issues
 */

// Start session
session_start();

// Output headers
echo "<h2>HTTP Headers</h2>";
echo "<pre>";
foreach (getallheaders() as $name => $value) {
    echo "$name: $value\n";
}
echo "</pre>";

// Output session data
echo "<h2>Session Data</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Output server variables
echo "<h2>Server Variables</h2>";
echo "<pre>";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
echo "SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "\n";
echo "PHP_SELF: " . $_SERVER['PHP_SELF'] . "\n";
echo "QUERY_STRING: " . ($_SERVER['QUERY_STRING'] ?? '') . "\n";
echo "HTTP_REFERER: " . ($_SERVER['HTTP_REFERER'] ?? '') . "\n";
echo "REDIRECT_STATUS: " . ($_SERVER['REDIRECT_STATUS'] ?? '') . "\n";
echo "</pre>";

// Output GET and POST data
echo "<h2>GET Data</h2>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

echo "<h2>POST Data</h2>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

// Output cookie data
echo "<h2>Cookie Data</h2>";
echo "<pre>";
print_r($_COOKIE);
echo "</pre>";

// Provide links to clear session and cookies
echo "<h2>Actions</h2>";
echo "<a href='debug.php?action=clear_session'>Clear Session</a> | ";
echo "<a href='debug.php?action=clear_cookies'>Clear Cookies</a> | ";
echo "<a href='debug.php?action=clear_all'>Clear All</a>";

// Handle actions
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'clear_session':
            session_unset();
            session_destroy();
            echo "<p>Session cleared. <a href='debug.php'>Refresh</a></p>";
            break;
        case 'clear_cookies':
            if (isset($_SERVER['HTTP_COOKIE'])) {
                $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
                foreach($cookies as $cookie) {
                    $parts = explode('=', $cookie);
                    $name = trim($parts[0]);
                    setcookie($name, '', time()-1000);
                    setcookie($name, '', time()-1000, '/');
                }
            }
            echo "<p>Cookies cleared. <a href='debug.php'>Refresh</a></p>";
            break;
        case 'clear_all':
            session_unset();
            session_destroy();
            if (isset($_SERVER['HTTP_COOKIE'])) {
                $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
                foreach($cookies as $cookie) {
                    $parts = explode('=', $cookie);
                    $name = trim($parts[0]);
                    setcookie($name, '', time()-1000);
                    setcookie($name, '', time()-1000, '/');
                }
            }
            echo "<p>Session and cookies cleared. <a href='debug.php'>Refresh</a></p>";
            break;
    }
}
?>

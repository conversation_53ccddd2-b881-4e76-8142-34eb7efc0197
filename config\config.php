<?php
/**
 * Application configuration
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'saas_rental');
define('DB_USER', 'root');
define('DB_PASS', '');

// Application configuration
define('APP_NAME', 'SAAS Service Rental');
define('APP_URL', 'http://localhost/ProjectUAS');

// Default controller and method
define('DEFAULT_CONTROLLER', 'Auth');
define('DEFAULT_METHOD', 'login');

// Debug mode (set to false in production)
define('DEBUG_MODE', true);

// Error reporting
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

<?php
/**
 * Standalone login page that doesn't rely on the MVC framework
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Clear any existing session data to prevent issues
session_unset();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Initialize error messages
$username_err = '';
$password_err = '';
$login_err = '';

// Process login form if submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    // Validate username
    if (empty($username)) {
        $username_err = 'Please enter username';
    }
    
    // Validate password
    if (empty($password)) {
        $password_err = 'Please enter password';
    }
    
    // If no validation errors, attempt to login
    if (empty($username_err) && empty($password_err)) {
        try {
            // Connect to database
            $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
            $options = [
                PDO::ATTR_PERSISTENT => true,
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
            ];
            
            $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // Get user by username
            $stmt = $conn->prepare('SELECT * FROM users WHERE username = :username');
            $stmt->bindParam(':username', $username);
            $stmt->execute();
            
            $user = $stmt->fetch();
            
            if ($user) {
                // Verify password
                if (password_verify($password, $user->password)) {
                    // Password is correct, create session
                    $_SESSION['user_id'] = $user->id;
                    $_SESSION['user_username'] = $user->username;
                    $_SESSION['user_email'] = $user->email;
                    $_SESSION['user_name'] = $user->full_name;
                    $_SESSION['user_role'] = $user->role;
                    
                    // If admin, set client ID
                    if ($user->role == 'admin' && $user->client_id) {
                        $_SESSION['client_id'] = $user->client_id;
                        
                        // Get client info
                        $stmt = $conn->prepare('SELECT * FROM clients WHERE id = :id');
                        $stmt->bindParam(':id', $user->client_id);
                        $stmt->execute();
                        
                        $client = $stmt->fetch();
                        if ($client) {
                            $_SESSION['client_name'] = $client->name;
                            $_SESSION['rental_type'] = $client->rental_type;
                        }
                    }
                    
                    // Reset redirect count
                    $_SESSION['redirect_count'] = 0;

                    // Redirect based on role
                    if ($user->role === 'superadmin') {
                        header('Location: ' . APP_URL . '/dashboard_direct.php');
                    } else {
                        header('Location: ' . APP_URL . '/admin_dashboard.php');
                    }
                    exit;
                } else {
                    // Password is incorrect
                    $login_err = 'Invalid username or password';
                }
            } else {
                // Username doesn't exist
                $login_err = 'Invalid username or password';
            }
        } catch (PDOException $e) {
            $login_err = 'Database error: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        .login-container {
            margin-top: 100px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .card-header {
            border-radius: 10px 10px 0 0;
        }
    </style>
</head>
<body>
    <div class="container login-container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center py-3">
                        <h4 class="mb-0">Login</h4>
                    </div>
                    <div class="card-body p-4">
                        <?php if (!empty($login_err)) : ?>
                            <div class="alert alert-danger"><?php echo $login_err; ?></div>
                        <?php endif; ?>
                        
                        <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" id="username" name="username" value="<?php echo $username ?? ''; ?>">
                                <div class="invalid-feedback"><?php echo $username_err; ?></div>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" id="password" name="password">
                                <div class="invalid-feedback"><?php echo $password_err; ?></div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Login</button>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center py-3">
                        <div class="small">
                            <p class="mb-0">Default credentials:</p>
                            <p class="mb-0">Username: superadmin</p>
                            <p class="mb-0">Password: password</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

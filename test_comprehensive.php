<?php
/**
 * Comprehensive Test Script for SAAS Rental Service
 * This script tests all major components of the system
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Define base path
define('BASE_PATH', __DIR__);

// Load configuration
require_once BASE_PATH . '/config/config.php';

// Test results array
$tests = [];
$passed = 0;
$failed = 0;

function addTest($name, $result, $message = '') {
    global $tests, $passed, $failed;
    $tests[] = [
        'name' => $name,
        'result' => $result,
        'message' => $message
    ];
    if ($result) {
        $passed++;
    } else {
        $failed++;
    }
}

// Test 1: Database Connection
try {
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    addTest('Database Connection', true, 'Successfully connected to database');
} catch (PDOException $e) {
    addTest('Database Connection', false, 'Failed: ' . $e->getMessage());
    die('Cannot continue without database connection');
}

// Test 2: Check if all required tables exist
$required_tables = ['users', 'clients', 'services', 'menus', 'submenus', 'client_services', 'client_menus', 'client_submenus', 'items', 'rentals'];
$existing_tables = [];

try {
    $stmt = $conn->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }
    
    $missing_tables = array_diff($required_tables, $existing_tables);
    if (empty($missing_tables)) {
        addTest('Database Tables', true, 'All required tables exist');
    } else {
        addTest('Database Tables', false, 'Missing tables: ' . implode(', ', $missing_tables));
    }
} catch (PDOException $e) {
    addTest('Database Tables', false, 'Error checking tables: ' . $e->getMessage());
}

// Test 3: Check if superadmin user exists
try {
    $stmt = $conn->query('SELECT * FROM users WHERE username = "superadmin"');
    $user = $stmt->fetch();
    
    if ($user) {
        addTest('Superadmin User', true, 'Superadmin user exists');
        
        // Test password verification
        if (password_verify('password', $user->password)) {
            addTest('Superadmin Password', true, 'Password verification successful');
        } else {
            addTest('Superadmin Password', false, 'Password verification failed');
        }
    } else {
        addTest('Superadmin User', false, 'Superadmin user not found');
    }
} catch (PDOException $e) {
    addTest('Superadmin User', false, 'Error: ' . $e->getMessage());
}

// Test 4: Check if default clients exist
try {
    $stmt = $conn->query('SELECT * FROM clients');
    $clients = $stmt->fetchAll();
    
    if (count($clients) >= 2) {
        addTest('Default Clients', true, 'Found ' . count($clients) . ' clients');
        
        // Check for bicycle and motorcycle clients
        $bicycle_client = false;
        $motorcycle_client = false;
        foreach ($clients as $client) {
            if ($client->rental_type == 'bicycle') $bicycle_client = true;
            if ($client->rental_type == 'motorcycle') $motorcycle_client = true;
        }
        
        if ($bicycle_client && $motorcycle_client) {
            addTest('Client Types', true, 'Both bicycle and motorcycle clients exist');
        } else {
            addTest('Client Types', false, 'Missing client types');
        }
    } else {
        addTest('Default Clients', false, 'Not enough clients found');
    }
} catch (PDOException $e) {
    addTest('Default Clients', false, 'Error: ' . $e->getMessage());
}

// Test 5: Check if default services exist
try {
    $stmt = $conn->query('SELECT * FROM services');
    $services = $stmt->fetchAll();
    
    if (count($services) >= 4) {
        addTest('Default Services', true, 'Found ' . count($services) . ' services');
    } else {
        addTest('Default Services', false, 'Not enough services found');
    }
} catch (PDOException $e) {
    addTest('Default Services', false, 'Error: ' . $e->getMessage());
}

// Test 6: Check if default menus exist
try {
    $stmt = $conn->query('SELECT * FROM menus');
    $menus = $stmt->fetchAll();
    
    if (count($menus) >= 10) {
        addTest('Default Menus', true, 'Found ' . count($menus) . ' menus');
    } else {
        addTest('Default Menus', false, 'Not enough menus found');
    }
} catch (PDOException $e) {
    addTest('Default Menus', false, 'Error: ' . $e->getMessage());
}

// Test 7: Check if client-service mappings exist
try {
    $stmt = $conn->query('SELECT * FROM client_services');
    $client_services = $stmt->fetchAll();
    
    if (count($client_services) >= 6) {
        addTest('Client-Service Mappings', true, 'Found ' . count($client_services) . ' mappings');
    } else {
        addTest('Client-Service Mappings', false, 'Not enough mappings found');
    }
} catch (PDOException $e) {
    addTest('Client-Service Mappings', false, 'Error: ' . $e->getMessage());
}

// Test 8: Check if client-menu mappings exist
try {
    $stmt = $conn->query('SELECT * FROM client_menus');
    $client_menus = $stmt->fetchAll();
    
    if (count($client_menus) >= 10) {
        addTest('Client-Menu Mappings', true, 'Found ' . count($client_menus) . ' mappings');
    } else {
        addTest('Client-Menu Mappings', false, 'Not enough mappings found');
    }
} catch (PDOException $e) {
    addTest('Client-Menu Mappings', false, 'Error: ' . $e->getMessage());
}

// Test 9: Check if core files exist
$core_files = [
    'config/config.php',
    'config/autoload.php',
    'config/router.php',
    'core/Database.php',
    'core/Model.php',
    'core/Controller.php',
    'models/User.php',
    'models/Client.php',
    'controllers/Auth.php',
    'controllers/Dashboard.php'
];

$missing_files = [];
foreach ($core_files as $file) {
    if (!file_exists(BASE_PATH . '/' . $file)) {
        $missing_files[] = $file;
    }
}

if (empty($missing_files)) {
    addTest('Core Files', true, 'All core files exist');
} else {
    addTest('Core Files', false, 'Missing files: ' . implode(', ', $missing_files));
}

// Test 10: Test autoloader
try {
    require_once BASE_PATH . '/config/autoload.php';
    addTest('Autoloader', true, 'Autoloader loaded successfully');
} catch (Exception $e) {
    addTest('Autoloader', false, 'Error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Test Results - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Comprehensive Test Results</h4>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5>Passed</h5>
                                        <h2><?php echo $passed; ?></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h5>Failed</h5>
                                        <h2><?php echo $failed; ?></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5>Total</h5>
                                        <h2><?php echo count($tests); ?></h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Test Name</th>
                                        <th>Status</th>
                                        <th>Message</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tests as $test) : ?>
                                        <tr>
                                            <td><?php echo $test['name']; ?></td>
                                            <td>
                                                <?php if ($test['result']) : ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check"></i> PASS
                                                    </span>
                                                <?php else : ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times"></i> FAIL
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $test['message']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Next Steps:</h5>
                            <?php if ($failed == 0) : ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> All tests passed! Your system is ready to use.
                                    <br><br>
                                    <a href="login.php" class="btn btn-success">Go to Login Page</a>
                                    <a href="setup_database.php" class="btn btn-info">Setup Database</a>
                                </div>
                            <?php else : ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Some tests failed. Please fix the issues above.
                                    <br><br>
                                    <a href="setup_database.php" class="btn btn-primary">Run Database Setup</a>
                                    <a href="test_comprehensive.php" class="btn btn-secondary">Re-run Tests</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

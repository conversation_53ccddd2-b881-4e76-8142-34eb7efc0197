<?php
/**
 * Test Admin Users - Check if admin users are properly created
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load configuration
require_once __DIR__ . '/config/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Admin Users</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<div class='card shadow'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h4 class='mb-0'>Admin Users Test</h4>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Connect to database
    $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
    $options = [
        PDO::ATTR_PERSISTENT => true,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ
    ];
    
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    echo "<p class='text-success'>✅ Database connection successful!</p>";
    
    // Get all users with client information
    $stmt = $conn->query('SELECT u.*, c.name as client_name, c.rental_type 
                         FROM users u 
                         LEFT JOIN clients c ON u.client_id = c.id 
                         ORDER BY u.role DESC, u.created_at DESC');
    $users = $stmt->fetchAll();
    
    echo "<h5>All Users in Database:</h5>";
    
    if (!empty($users)) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-bordered table-striped'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Username</th>";
        echo "<th>Full Name</th>";
        echo "<th>Email</th>";
        echo "<th>Role</th>";
        echo "<th>Client</th>";
        echo "<th>Created</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user->id . "</td>";
            echo "<td><strong>" . htmlspecialchars($user->username) . "</strong></td>";
            echo "<td>" . htmlspecialchars($user->full_name) . "</td>";
            echo "<td>" . htmlspecialchars($user->email) . "</td>";
            echo "<td>";
            if ($user->role === 'superadmin') {
                echo "<span class='badge bg-danger'>Superadmin</span>";
            } else {
                echo "<span class='badge bg-primary'>Admin</span>";
            }
            echo "</td>";
            echo "<td>";
            if ($user->client_name) {
                echo htmlspecialchars($user->client_name);
                echo "<br><small class='text-muted'>" . ucfirst($user->rental_type) . "</small>";
            } else {
                echo "<span class='text-muted'>N/A</span>";
            }
            echo "</td>";
            echo "<td><small>" . date('d M Y H:i', strtotime($user->created_at)) . "</small></td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        
        // Count users by role
        $superadmin_count = 0;
        $admin_count = 0;
        
        foreach ($users as $user) {
            if ($user->role === 'superadmin') {
                $superadmin_count++;
            } else {
                $admin_count++;
            }
        }
        
        echo "<div class='row mt-4'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h5>Superadmin Users</h5>";
        echo "<h2>$superadmin_count</h2>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-primary text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h5>Admin Users</h5>";
        echo "<h2>$admin_count</h2>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
    } else {
        echo "<p class='text-warning'>⚠️ No users found in database!</p>";
    }
    
    // Test login for each admin user
    echo "<hr>";
    echo "<h5>Admin Login Test:</h5>";
    
    $admin_users = array_filter($users, function($user) {
        return $user->role === 'admin';
    });
    
    if (!empty($admin_users)) {
        echo "<div class='alert alert-info'>";
        echo "<strong>Admin users found:</strong><br>";
        foreach ($admin_users as $admin) {
            echo "• Username: <code>" . htmlspecialchars($admin->username) . "</code> - Client: " . htmlspecialchars($admin->client_name) . "<br>";
        }
        echo "</div>";
        
        echo "<p>You can now login with these admin accounts using the password you set when creating them.</p>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "⚠️ No admin users found. You need to create admin users first.";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p class='text-danger'>❌ Database Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h5>Next Steps:</h5>";
echo "<div class='d-grid gap-2 d-md-block'>";
echo "<a href='add_admin.php' class='btn btn-success'>Add New Admin</a> ";
echo "<a href='users_list.php' class='btn btn-info'>View Users List</a> ";
echo "<a href='dashboard_direct.php' class='btn btn-primary'>Go to Dashboard</a> ";
echo "<a href='login.php' class='btn btn-secondary'>Login Page</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body></html>";
?>

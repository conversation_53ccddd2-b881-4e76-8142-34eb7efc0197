<?php
/**
 * Services Controller
 */

class Services extends Controller {
    private $serviceModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!$this->isLoggedIn()) {
            $this->redirect('auth/login');
        }
        
        // Check if user is superadmin
        if (!$this->hasRole('superadmin')) {
            $this->redirect('dashboard');
        }
        
        $this->serviceModel = $this->model('Service');
    }
    
    /**
     * Services index
     */
    public function index() {
        // Get all services
        $services = $this->serviceModel->getAllServices();
        
        $data = [
            'title' => 'Service Management',
            'services' => $services
        ];
        
        $this->view('services/index', $data);
    }
    
    /**
     * Add service
     */
    public function add() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['status_err'])) {
                // Create service
                $serviceId = $this->serviceModel->createService($data);
                
                if ($serviceId) {
                    // Service created
                    // Set flash message
                    // Redirect to services index
                    $this->redirect('services');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Add Service';
                
                $this->view('services/add', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Add Service',
                'name' => '',
                'description' => '',
                'status' => 'active',
                'name_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('services/add', $data);
        }
    }
    
    /**
     * Edit service
     * 
     * @param int $id Service ID
     */
    public function edit($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Init data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'status' => trim($_POST['status']),
                'name_err' => '',
                'status_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter name';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select status';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['status_err'])) {
                // Update service
                if ($this->serviceModel->updateService($data)) {
                    // Service updated
                    // Set flash message
                    // Redirect to services index
                    $this->redirect('services');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Edit Service';
                
                $this->view('services/edit', $data);
            }
        } else {
            // Get service
            $service = $this->serviceModel->getServiceById($id);
            
            // Check if service exists
            if (!$service) {
                $this->redirect('services');
            }
            
            // Init data
            $data = [
                'title' => 'Edit Service',
                'id' => $service->id,
                'name' => $service->name,
                'description' => $service->description,
                'status' => $service->status,
                'name_err' => '',
                'status_err' => ''
            ];
            
            // Load view
            $this->view('services/edit', $data);
        }
    }
    
    /**
     * Delete service
     * 
     * @param int $id Service ID
     */
    public function delete($id) {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete service
            if ($this->serviceModel->deleteService($id)) {
                // Service deleted
                // Set flash message
                // Redirect to services index
                $this->redirect('services');
            } else {
                die('Something went wrong');
            }
        } else {
            $this->redirect('services');
        }
    }
}

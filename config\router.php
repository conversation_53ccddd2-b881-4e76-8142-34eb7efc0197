<?php
/**
 * Router class for handling URL routing
 */

class Router {
    private $controller;
    private $method;
    private $params = [];

    public function __construct() {
        $url = $this->parseUrl();

        // Set default controller if URL is empty
        if (empty($url[0])) {
            $this->controller = DEFAULT_CONTROLLER;
        } else {
            // Check if controller exists
            if (file_exists(BASE_PATH . '/controllers/' . ucfirst($url[0]) . '.php')) {
                $this->controller = ucfirst($url[0]);
                unset($url[0]);
            } else {
                // Controller not found, use default
                $this->controller = DEFAULT_CONTROLLER;
            }
        }

        // Load the controller
        require_once BASE_PATH . '/controllers/' . $this->controller . '.php';
        $this->controller = new $this->controller();

        // Check for method
        if (isset($url[1])) {
            if (method_exists($this->controller, $url[1])) {
                $this->method = $url[1];
                unset($url[1]);
            } else {
                // Method not found, use default
                $this->method = DEFAULT_METHOD;
            }
        } else {
            // No method specified, use default
            $this->method = DEFAULT_METHOD;
        }

        // Get parameters
        $this->params = $url ? array_values($url) : [];

        // Check if the method exists in the controller
        if (!method_exists($this->controller, $this->method)) {
            // If we're in Auth controller and method doesn't exist, use login
            if (get_class($this->controller) === 'Auth') {
                $this->method = 'login';
            }
            // If index method exists, use it
            else if (method_exists($this->controller, 'index')) {
                $this->method = 'index';
            }
            // Last resort: create a new Auth controller and use login method
            else {
                // To prevent infinite loops, check if we're already trying to access Auth
                if (isset($_GET['url']) && strpos($_GET['url'], 'auth') !== false) {
                    die('Error: Method not found and cannot redirect to Auth controller.');
                }

                $this->controller = new Auth();
                $this->method = 'login';
            }
        }
    }

    public function dispatch() {
        // Call the controller method with parameters
        call_user_func_array([$this->controller, $this->method], $this->params);
    }

    private function parseUrl() {
        if (isset($_GET['url'])) {
            // Trim trailing slash, filter URL, and explode
            return explode('/', filter_var(rtrim($_GET['url'], '/'), FILTER_SANITIZE_URL));
        }

        return [];
    }
}
